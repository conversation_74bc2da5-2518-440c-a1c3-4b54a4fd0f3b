using Microsoft.EntityFrameworkCore;
using HarmoniHSE360.Modules.UserManagement.Application.Contracts;
using HarmoniHSE360.Modules.UserManagement.Domain.Users;

namespace HarmoniHSE360.Modules.UserManagement.Infrastructure.Persistence.Repositories;

public sealed class UserLoginRepository : IUserLoginRepository
{
    private readonly UserManagementDbContext _context;

    public UserLoginRepository(UserManagementDbContext context)
    {
        _context = context;
    }

    public async Task AddAsync(UserLogin userLogin, CancellationToken cancellationToken = default)
    {
        await _context.UserLogins.AddAsync(userLogin, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<IReadOnlyList<UserLogin>> GetUserLoginsAsync(Guid userId, int skip = 0, int take = 50, CancellationToken cancellationToken = default)
    {
        return await _context.UserLogins
            .Where(ul => ul.UserId == userId)
            .OrderByDescending(ul => ul.AttemptedAt)
            .Skip(skip)
            .Take(take)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> GetFailedLoginAttemptsCountAsync(Guid userId, DateTime since, CancellationToken cancellationToken = default)
    {
        return await _context.UserLogins
            .CountAsync(ul => ul.UserId == userId 
                && !ul.IsSuccessful 
                && ul.AttemptedAt >= since, 
                cancellationToken);
    }
}