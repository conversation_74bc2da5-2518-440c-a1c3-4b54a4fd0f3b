// HarmoniHSE360 Default Theme
// Light theme color scheme and CSS variables

:root {
  // Brand Colors
  --harmoni-teal-primary: #{$harmoni-teal-primary};
  --harmoni-deep-blue: #{$harmoni-deep-blue};
  --harmoni-leaf-green: #{$harmoni-leaf-green};
  --harmoni-sunset-orange: #{$harmoni-sunset-orange};
  
  // Neutral Colors
  --harmoni-cool-grey: #{$harmoni-cool-grey};
  --harmoni-charcoal: #{$harmoni-charcoal};
  --harmoni-soft-white: #{$harmoni-soft-white};
  --harmoni-border-grey: #{$harmoni-border-grey};
  
  // Status Colors
  --harmoni-error: #{$harmoni-error};
  --harmoni-warning: #{$harmoni-warning};
  --harmoni-success: #{$harmoni-success};
  --harmoni-info: #{$harmoni-info};
  
  // Typography
  --harmoni-font-primary: #{$harmoni-font-primary};
  --harmoni-font-secondary: #{$harmoni-font-secondary};
  --harmoni-font-mono: #{$harmoni-font-mono};
  
  // Spacing
  --harmoni-spacing-xs: #{$harmoni-spacing-xs};
  --harmoni-spacing-sm: #{$harmoni-spacing-sm};
  --harmoni-spacing-md: #{$harmoni-spacing-md};
  --harmoni-spacing-lg: #{$harmoni-spacing-lg};
  --harmoni-spacing-xl: #{$harmoni-spacing-xl};
  --harmoni-spacing-2xl: #{$harmoni-spacing-2xl};
  --harmoni-spacing-3xl: #{$harmoni-spacing-3xl};
  
  // Border Radius
  --harmoni-radius-sm: #{$harmoni-radius-sm};
  --harmoni-radius-md: #{$harmoni-radius-md};
  --harmoni-radius-lg: #{$harmoni-radius-lg};
  --harmoni-radius-xl: #{$harmoni-radius-xl};
  --harmoni-radius-full: #{$harmoni-radius-full};
  
  // Shadows
  --harmoni-shadow-sm: #{$harmoni-shadow-sm};
  --harmoni-shadow-md: #{$harmoni-shadow-md};
  --harmoni-shadow-lg: #{$harmoni-shadow-lg};
  --harmoni-shadow-xl: #{$harmoni-shadow-xl};
  
  // Transitions
  --harmoni-transition-fast: #{$harmoni-transition-fast};
  --harmoni-transition: #{$harmoni-transition};
  --harmoni-transition-slow: #{$harmoni-transition-slow};
  
  // Component-specific colors
  --harmoni-bg-primary: #{$harmoni-soft-white};
  --harmoni-bg-secondary: white;
  --harmoni-bg-accent: #{rgba($harmoni-teal-primary, 0.05)};
  --harmoni-text-primary: #{$harmoni-charcoal};
  --harmoni-text-secondary: #{$harmoni-cool-grey};
  --harmoni-text-inverse: white;
  --harmoni-border-color: #{$harmoni-border-grey};
  --harmoni-hover-bg: #{rgba($harmoni-teal-primary, 0.08)};
  --harmoni-selected-bg: #{rgba($harmoni-teal-primary, 0.12)};
  
  // Card styles
  --harmoni-card-bg: white;
  --harmoni-card-shadow: #{$harmoni-shadow-md};
  --harmoni-card-border: none;
  
  // Input styles
  --harmoni-input-bg: white;
  --harmoni-input-border: #{$harmoni-border-grey};
  --harmoni-input-focus-border: #{$harmoni-teal-primary};
  --harmoni-input-focus-shadow: #{rgba($harmoni-teal-primary, 0.2)};
  
  // Button styles
  --harmoni-btn-primary-bg: #{$harmoni-teal-primary};
  --harmoni-btn-primary-hover: #{darken($harmoni-teal-primary, 10%)};
  --harmoni-btn-secondary-bg: #{$harmoni-deep-blue};
  --harmoni-btn-secondary-hover: #{darken($harmoni-deep-blue, 10%)};
  
  // Navigation styles
  --harmoni-nav-bg: white;
  --harmoni-nav-link: #{$harmoni-charcoal};
  --harmoni-nav-link-hover: #{$harmoni-teal-primary};
  --harmoni-nav-link-active: #{$harmoni-teal-primary};
  
  // Sidebar styles
  --harmoni-sidebar-bg: #{$harmoni-deep-blue};
  --harmoni-sidebar-text: white;
  --harmoni-sidebar-hover: #{rgba(white, 0.1)};
  --harmoni-sidebar-active: #{rgba(white, 0.15)};
  
  // Table styles
  --harmoni-table-bg: white;
  --harmoni-table-hover: #{rgba($harmoni-teal-primary, 0.05)};
  --harmoni-table-stripe: #{$harmoni-soft-white};
  --harmoni-table-border: #{$harmoni-border-grey};
}