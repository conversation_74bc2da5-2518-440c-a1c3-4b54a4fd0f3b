@page "/login"
@using Microsoft.AspNetCore.Components.Forms
@using HarmoniHSE360.BlazorServer.Services
@using AntDesign
@using System.ComponentModel.DataAnnotations
@attribute [Microsoft.AspNetCore.Authorization.AllowAnonymous]
@rendermode InteractiveServer
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@inject AuthService AuthService

<PageTitle>Login - HarmoniHSE360</PageTitle>

<div class="login-container">
    <div class="login-wrapper">
        <Card class="login-card">
            <div class="brand-header">
                <div class="brand-logo">
                    <Title Level="1" class="brand-title">HarmoniHSE360</Title>
                    <Text Type="@TextElementType.Secondary" class="brand-subtitle">Complete Safety. Seamless Harmony.</Text>
                </div>
            </div>
            
            <EditForm Model="@loginModel" OnValidSubmit="@HandleLogin" class="login-form" @onsubmit:preventDefault="true">
                
                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <Alert Type="@AlertType.Error" 
                           Message="@errorMessage" 
                           ShowIcon="true" 
                           Closable="true"
                           OnClose="@(() => errorMessage = string.Empty)" 
                           Style="margin-bottom: 16px;" />
                }
                
                @if (!string.IsNullOrEmpty(successMessage))
                {
                    <Alert Type="@AlertType.Success" 
                           Message="@successMessage" 
                           ShowIcon="true" 
                           Closable="true"
                           OnClose="@(() => successMessage = string.Empty)" 
                           Style="margin-bottom: 16px;" />
                }
                
                <FormItem Label="Email Address" Required="true">
                    <Input @bind-Value="@loginModel.Email"
                           Type="email"
                           Placeholder="Enter your email address"
                           Size="@InputSize.Large"
                           AllowClear="true"
                           Id="email">
                        <Prefix>
                            <Icon Type="user" />
                        </Prefix>
                    </Input>
                </FormItem>

                <FormItem Label="Password" Required="true">
                    <InputPassword @bind-Value="@loginModel.Password"
                                   Placeholder="Enter your password"
                                   Size="@InputSize.Large"
                                   VisibilityToggle="true">
                        <Prefix>
                            <Icon Type="lock" />
                        </Prefix>
                    </InputPassword>
                </FormItem>
                
                <FormItem>
                    <Checkbox @bind-Value="@loginModel.RememberMe">
                        Remember me
                    </Checkbox>
                </FormItem>
                
                <FormItem>
                    <Button Type="@ButtonType.Primary" 
                            HtmlType="submit"
                            Size="@ButtonSize.Large"
                            Block="true"
                            Loading="@isLoading">
                        Sign In
                    </Button>
                </FormItem>
                
                <div class="additional-actions">
                    <Text>
                        <a href="/forgot-password" class="forgot-password-link">Forgot your password?</a>
                    </Text>
                    
                    <Divider>Or</Divider>
                    
                    <Space Direction="DirectionVHType.Vertical" Size="@("small")" Style="width: 100%;">
                        <SpaceItem>
                            <Button Type="@ButtonType.Default" 
                                    Size="@ButtonSize.Large"
                                    Block="true"
                                    OnClick="@HandleGoogleLogin">
                                <Icon Type="google" />
                                Sign in with Google
                            </Button>
                        </SpaceItem>
                        <SpaceItem>
                            <Button Type="@ButtonType.Default" 
                                    Size="@ButtonSize.Large"
                                    Block="true"
                                    OnClick="@HandleAzureLogin">
                                <Icon Type="windows" />
                                Sign in with Microsoft
                            </Button>
                        </SpaceItem>
                    </Space>
                </div>
                
                <div class="register-link">
                    <Text>
                        Don't have an account? 
                        <a href="/register" class="register-link-text">Create one here</a>
                    </Text>
                </div>
                
                <div class="demo-credentials" style="margin-top: 20px; padding: 16px; background: #f5f5f5; border-radius: 6px;">
                    <Text Type="@TextElementType.Secondary" Style="font-size: 12px; font-weight: bold;">Demo Credentials (Password: HarmoniHSE360!):</Text>
                    <div style="margin-top: 8px; font-size: 11px; line-height: 1.4;">
                        <div><strong>Admin:</strong> <EMAIL></div>
                        <div><strong>HSE Manager:</strong> <EMAIL></div>
                        <div><strong>Employee:</strong> <EMAIL></div>
                        <div><strong>Safety Officer:</strong> <EMAIL></div>
                    </div>
                </div>

                <DataAnnotationsValidator />
                <ValidationSummary />
            </EditForm>
        </Card>
    </div>
</div>

@code {
    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    private LoginModel loginModel = new();
    private bool isLoading = false;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JSRuntime.InvokeVoidAsync("harmoni.focusElement", "email");
        }
    }

    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;
            StateHasChanged();
            
            // Call the authentication service
            var result = await AuthService.LoginAsync(loginModel.Email, loginModel.Password);
            
            if (result.Success)
            {
                if (result.RequiresTwoFactor)
                {
                    // Redirect to 2FA page
                    Navigation.NavigateTo("/two-factor-auth");
                }
                else
                {
                    successMessage = "Login successful! Redirecting...";
                    StateHasChanged();
                    await Task.Delay(1000);

                    // Use forceLoad to ensure proper navigation and prevent form data in URL
                    Navigation.NavigateTo(ReturnUrl ?? "/dashboard", forceLoad: true);
                }
            }
            else
            {
                errorMessage = result.ErrorMessage ?? "Invalid email or password. Please try again.";
                StateHasChanged();
            }
        }
        catch (Exception)
        {
            errorMessage = "An error occurred during login. Please try again.";
            StateHasChanged();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task HandleLoginFailed(EditContext editContext)
    {
        errorMessage = "Please check your input and try again.";
        await InvokeAsync(StateHasChanged);
    }

    private async Task HandleGoogleLogin()
    {
        // TODO: Implement Google OAuth login
        await JSRuntime.InvokeVoidAsync("harmoni.showToast", "Google authentication will be implemented soon.", "info");
    }

    private async Task HandleAzureLogin()
    {
        // TODO: Implement Azure AD login
        await JSRuntime.InvokeVoidAsync("harmoni.showToast", "Microsoft authentication will be implemented soon.", "info");
    }

    public class LoginModel
    {
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Password must be at least 6 characters long")]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; } = false;
    }
}