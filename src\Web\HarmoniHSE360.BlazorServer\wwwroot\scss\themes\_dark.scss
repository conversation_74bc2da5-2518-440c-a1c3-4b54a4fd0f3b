// HarmoniHSE360 Dark Theme
// Dark theme color scheme

[data-theme="dark"] {
  // Component-specific colors
  --harmoni-bg-primary: #1a1a1a;
  --harmoni-bg-secondary: #2d2d2d;
  --harmoni-bg-accent: #{rgba($harmoni-teal-primary, 0.15)};
  --harmoni-text-primary: #f5f5f5;
  --harmoni-text-secondary: #b0b0b0;
  --harmoni-text-inverse: #1a1a1a;
  --harmoni-border-color: #404040;
  --harmoni-hover-bg: #{rgba($harmoni-teal-primary, 0.2)};
  --harmoni-selected-bg: #{rgba($harmoni-teal-primary, 0.3)};
  
  // Card styles
  --harmoni-card-bg: #2d2d2d;
  --harmoni-card-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  --harmoni-card-border: 1px solid #404040;
  
  // Input styles
  --harmoni-input-bg: #1a1a1a;
  --harmoni-input-border: #404040;
  --harmoni-input-focus-border: #{$harmoni-teal-primary};
  --harmoni-input-focus-shadow: #{rgba($harmoni-teal-primary, 0.4)};
  
  // Button styles (slightly lighter in dark theme)
  --harmoni-btn-primary-bg: #{lighten($harmoni-teal-primary, 5%)};
  --harmoni-btn-primary-hover: #{$harmoni-teal-primary};
  --harmoni-btn-secondary-bg: #{lighten($harmoni-deep-blue, 10%)};
  --harmoni-btn-secondary-hover: #{$harmoni-deep-blue};
  
  // Navigation styles
  --harmoni-nav-bg: #2d2d2d;
  --harmoni-nav-link: #f5f5f5;
  --harmoni-nav-link-hover: #{$harmoni-teal-primary};
  --harmoni-nav-link-active: #{$harmoni-teal-primary};
  
  // Sidebar styles
  --harmoni-sidebar-bg: #1a1a1a;
  --harmoni-sidebar-text: #f5f5f5;
  --harmoni-sidebar-hover: #{rgba($harmoni-teal-primary, 0.2)};
  --harmoni-sidebar-active: #{rgba($harmoni-teal-primary, 0.3)};
  
  // Table styles
  --harmoni-table-bg: #2d2d2d;
  --harmoni-table-hover: #{rgba($harmoni-teal-primary, 0.15)};
  --harmoni-table-stripe: #242424;
  --harmoni-table-border: #404040;
  
  // Update body background
  body {
    background-color: var(--harmoni-bg-primary);
    color: var(--harmoni-text-primary);
  }
  
  // Update link colors
  a {
    color: #{lighten($harmoni-teal-primary, 10%)};
    
    &:hover {
      color: #{lighten($harmoni-teal-primary, 20%)};
    }
  }
  
  // Update code blocks
  .code {
    background-color: rgba(255, 255, 255, 0.1);
    color: #{lighten($harmoni-teal-primary, 10%)};
  }
  
  // Update blockquotes
  blockquote {
    color: var(--harmoni-text-secondary);
    border-left-color: #{$harmoni-teal-primary};
  }
  
  // Dark theme specific utilities
  .bg-light {
    background-color: var(--harmoni-bg-secondary) !important;
  }
  
  .text-muted {
    color: var(--harmoni-text-secondary) !important;
  }
  
  // Adjust shadows for dark theme
  .shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  }
  
  .shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  }
  
  .shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
  }
  
  .shadow-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.5);
  }
}