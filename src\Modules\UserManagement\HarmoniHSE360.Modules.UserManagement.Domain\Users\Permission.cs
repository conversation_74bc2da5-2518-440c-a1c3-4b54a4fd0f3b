using HarmoniHSE360.BuildingBlocks.Domain;

namespace HarmoniHSE360.Modules.UserManagement.Domain.Users;

public sealed class Permission : Entity<Guid>
{
    public string Name { get; private set; }
    public string Module { get; private set; }
    public string Resource { get; private set; }
    public string Action { get; private set; }
    public string? Description { get; private set; }
    public DateTime CreatedAt { get; private set; }

    private Permission() { }

    private Permission(string module, string resource, string action, string? description = null)
    {
        Id = Guid.NewGuid();
        Module = module ?? throw new ArgumentNullException(nameof(module));
        Resource = resource ?? throw new ArgumentNullException(nameof(resource));
        Action = action ?? throw new ArgumentNullException(nameof(action));
        Name = $"{module}.{resource}.{action}";
        Description = description;
        CreatedAt = DateTime.UtcNow;
    }

    public static Permission Create(string module, string resource, string action, string? description = null)
    {
        return new Permission(module, resource, action, description);
    }

    public static class Modules
    {
        public const string IncidentManagement = "IncidentManagement";
        public const string HazardReporting = "HazardReporting";
        public const string ComplianceAudit = "ComplianceAudit";
        public const string DocumentManagement = "DocumentManagement";
        public const string PermitToWork = "PermitToWork";
        public const string Training = "Training";
        public const string Environmental = "Environmental";
        public const string Analytics = "Analytics";
        public const string UserManagement = "UserManagement";
        public const string System = "System";
    }

    public static class Actions
    {
        public const string View = "View";
        public const string Create = "Create";
        public const string Edit = "Edit";
        public const string Delete = "Delete";
        public const string Approve = "Approve";
        public const string Export = "Export";
        public const string Configure = "Configure";
    }
}