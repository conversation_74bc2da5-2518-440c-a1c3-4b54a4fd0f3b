version: '3.8'

services:
  timescaledb:
    image: timescale/timescaledb-ha:pg16
    container_name: harmonihse360-timescaledb
    environment:
      - POSTGRES_USER=harmonihse360
      - POSTGRES_PASSWORD=HarmoniHSE360!2024
      - POSTGRES_DB=harmonihse360_dev
      - TS_TUNE_MEMORY=4GB
      - TS_TUNE_NUM_CPUS=4
    ports:
      - "5432:5432"
    volumes:
      - timescale_data:/var/lib/postgresql/data
      - ./scripts/sql/init:/docker-entrypoint-initdb.d
    networks:
      - harmonihse360-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U harmonihse360"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: harmonihse360-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - harmonihse360-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  api:
    build:
      context: .
      dockerfile: src/API/HarmoniHSE360.Api/Dockerfile
    container_name: harmonihse360-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=timescaledb;Port=5432;Database=harmonihse360_dev;Username=harmonihse360;Password=HarmoniHSE360!2024
      - ConnectionStrings__Redis=redis:6379
    ports:
      - "5000:8080"
    depends_on:
      timescaledb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - harmonihse360-network
    volumes:
      - ./src:/src:ro

  blazor:
    build:
      context: .
      dockerfile: src/Web/HarmoniHSE360.BlazorServer/Dockerfile
    container_name: harmonihse360-blazor
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ApiBaseUrl=http://api:8080
      - ConnectionStrings__Redis=redis:6379
    ports:
      - "5001:8080"
    depends_on:
      - api
    networks:
      - harmonihse360-network
    volumes:
      - ./src:/src:ro

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: harmonihse360-pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=PgAdmin2024!
      - PGADMIN_CONFIG_SERVER_MODE=False
      - PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED=False
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - harmonihse360-network
    depends_on:
      - timescaledb

volumes:
  timescale_data:
  redis_data:
  pgadmin_data:

networks:
  harmonihse360-network:
    driver: bridge