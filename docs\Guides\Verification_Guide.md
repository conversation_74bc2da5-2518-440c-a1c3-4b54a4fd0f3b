# HarmoniHSE360 Application Verification Guide

## 🚀 After Running `docker-compose up`

### 1. Check Service Status

Run this command to verify all services are running:
```bash
docker-compose ps
```

You should see 4 services running:
- ✅ **harmonihse360-timescaledb** - PostgreSQL database (port 5432)
- ✅ **harmonihse360-redis** - Redis cache (port 6379)
- ✅ **harmonihse360-api** - API service (port 5000)
- ✅ **harmonihse360-blazor** - Blazor web app (port 5001)

### 2. Access the Applications

Open your web browser and navigate to:

#### 🌐 Blazor Web Application
```
http://localhost:5001
```
This is the main web interface for HarmoniHSE360.

#### 🔧 API Documentation (Swagger)
```
http://localhost:5000/swagger
```
This shows the API documentation and allows you to test API endpoints.

### 3. Verify Database Connection

Check if TimescaleDB is running properly:
```bash
docker exec -it harmonihse360-timescaledb psql -U harmonihse360 -d harmonihse360_dev
```

Once connected, you can run:
```sql
\dt  -- List tables
\q   -- Quit
```

### 4. Verify Redis Connection

Test Redis connectivity:
```bash
docker exec -it harmonihse360-redis redis-cli ping
```
You should see: `PONG`

## 🔍 What You Should See

### On First Access (http://localhost:5001)

Since this is a fresh setup, you'll see the default Blazor template page. The actual HarmoniHSE360 features are not yet implemented, but the infrastructure is ready.

### Expected Default Pages:
1. **Home** - Welcome page
2. **Counter** - Sample interactive component
3. **Weather** - Sample data fetching

## 🛠️ Troubleshooting

### If Services Don't Start:

1. **Check Docker logs:**
   ```bash
   docker-compose logs [service-name]
   ```

2. **Common Issues:**
   - Port conflicts: Make sure ports 5000, 5001, 5432, and 6379 are not in use
   - Memory issues: Docker Desktop needs at least 4GB RAM allocated
   - Path issues on Windows: Use WSL2 or ensure proper path mapping

3. **Restart services:**
   ```bash
   docker-compose down
   docker-compose up -d
   ```

### If Pages Don't Load:

1. **Check firewall:** Ensure localhost connections are allowed
2. **Try different browser:** Sometimes browser cache causes issues
3. **Check container logs:** 
   ```bash
   docker-compose logs -f blazor
   docker-compose logs -f api
   ```

## 📝 Next Steps

1. **Implement Authentication UI**
   - Login page
   - Registration page
   - User profile

2. **Create Initial Database Schema**
   - Run Entity Framework migrations
   - Seed initial data (roles, permissions)

3. **Build First Module**
   - Start with User Management UI
   - Add role management
   - Implement permission assignment

4. **Test Authentication Flow**
   - Register a new user
   - Login with local authentication
   - Verify role-based access

## 🎯 Development Workflow

For active development, you might want to run services individually:

1. **Run only infrastructure:**
   ```bash
   docker-compose up -d timescaledb redis
   ```

2. **Run .NET apps locally:**
   ```bash
   cd src/API/HarmoniHSE360.Api
   dotnet run
   
   # In another terminal
   cd src/Web/HarmoniHSE360.BlazorServer
   dotnet run
   ```

This allows for faster development with hot reload.

## 📊 Monitoring

### View real-time logs:
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f api
```

### Check resource usage:
```bash
docker stats
```

---

**Note:** The application is currently showing default .NET templates. The actual HarmoniHSE360 features need to be implemented according to the development plan.