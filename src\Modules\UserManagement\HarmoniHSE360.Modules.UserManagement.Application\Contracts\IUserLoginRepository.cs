using HarmoniHSE360.Modules.UserManagement.Domain.Users;

namespace HarmoniHSE360.Modules.UserManagement.Application.Contracts;

public interface IUserLoginRepository
{
    Task AddAsync(UserLogin userLogin, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<UserLogin>> GetUserLoginsAsync(Guid userId, int skip = 0, int take = 50, CancellationToken cancellationToken = default);
    Task<int> GetFailedLoginAttemptsCountAsync(Guid userId, DateTime since, CancellationToken cancellationToken = default);
}