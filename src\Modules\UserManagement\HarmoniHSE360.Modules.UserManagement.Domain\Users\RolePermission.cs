using HarmoniHSE360.BuildingBlocks.Domain;

namespace HarmoniHSE360.Modules.UserManagement.Domain.Users;

public sealed class RolePermission : Entity<Guid>
{
    public Guid RoleId { get; private set; }
    public Guid PermissionId { get; private set; }
    public DateTime GrantedAt { get; private set; }
    public string? GrantedBy { get; private set; }

    public Role Role { get; private set; } = null!;
    public Permission Permission { get; private set; } = null!;

    private RolePermission() { }

    private RolePermission(Guid roleId, Guid permissionId, string? grantedBy)
    {
        Id = Guid.NewGuid();
        RoleId = roleId;
        PermissionId = permissionId;
        GrantedBy = grantedBy;
        GrantedAt = DateTime.UtcNow;
    }

    public static RolePermission Create(Guid roleId, Guid permissionId, string? grantedBy = null)
    {
        return new RolePermission(roleId, permissionId, grantedBy);
    }
}