.navbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--harmoni-spacing-lg);
}

.navbar-brand-section {
    flex-shrink: 0;
}

.brand-logo-nav {
    display: flex;
    align-items: center;
    gap: var(--harmoni-spacing-sm);
}

.brand-icon {
    font-size: 24px;
}

.brand-text {
    font-weight: 700;
    font-size: 20px;
}

.navbar-menu {
    flex: 1;
    display: flex;
    justify-content: center;
}

.harmoni-navbar-nav {
    display: flex;
    align-items: center;
    gap: var(--harmoni-spacing-md);
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.harmoni-navbar-link {
    display: flex;
    align-items: center;
    gap: var(--harmoni-spacing-xs);
    padding: var(--harmoni-spacing-sm) var(--harmoni-spacing-md);
    border-radius: var(--harmoni-radius-md);
    text-decoration: none;
    color: var(--harmoni-charcoal);
    font-weight: 500;
    font-size: 14px;
    transition: var(--harmoni-transition);
    border: none;
    background: none;
    cursor: pointer;
    font-family: var(--harmoni-font-primary);
}

.harmoni-navbar-link:hover,
.harmoni-navbar-link.active {
    background-color: rgba(0, 151, 167, 0.1);
    color: var(--harmoni-teal-primary);
}

.nav-icon {
    font-size: 16px;
}

.dropdown-toggle .dropdown-arrow {
    font-size: 10px;
    transition: var(--harmoni-transition);
}

.dropdown-toggle:hover .dropdown-arrow,
.dropdown-toggle.open .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background-color: var(--harmoni-white);
    border-radius: var(--harmoni-radius-md);
    box-shadow: var(--harmoni-shadow-lg);
    border: 1px solid var(--harmoni-border-grey);
    padding: var(--harmoni-spacing-sm) 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--harmoni-transition);
    z-index: 1000;
    list-style: none;
    margin: 0;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: block;
    padding: var(--harmoni-spacing-sm) var(--harmoni-spacing-md);
    color: var(--harmoni-charcoal);
    text-decoration: none;
    font-size: 14px;
    transition: var(--harmoni-transition);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-family: var(--harmoni-font-primary);
}

.dropdown-link:hover {
    background-color: var(--harmoni-soft-grey);
    color: var(--harmoni-teal-primary);
}

.navbar-actions {
    display: flex;
    align-items: center;
    gap: var(--harmoni-spacing-md);
    flex-shrink: 0;
}

.user-menu-toggle {
    display: flex;
    align-items: center;
    gap: var(--harmoni-spacing-sm);
    padding: var(--harmoni-spacing-sm);
    border: none;
    background: none;
    cursor: pointer;
    border-radius: var(--harmoni-radius-md);
    transition: var(--harmoni-transition);
    font-family: var(--harmoni-font-primary);
}

.user-menu-toggle:hover {
    background-color: var(--harmoni-soft-grey);
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--harmoni-teal-primary), var(--harmoni-leaf-green));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--harmoni-white);
    font-weight: 600;
    font-size: 14px;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 0;
}

.user-name {
    font-weight: 600;
    font-size: 14px;
    color: var(--harmoni-charcoal);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.user-role {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.user-dropdown {
    right: 0;
    left: auto;
    min-width: 220px;
}

.dropdown-header {
    padding: var(--harmoni-spacing-md);
    border-bottom: 1px solid var(--harmoni-border-grey);
    margin-bottom: var(--harmoni-spacing-sm);
}

.user-details strong {
    display: block;
    font-size: 14px;
    color: var(--harmoni-charcoal);
}

.user-details small {
    display: block;
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.dropdown-divider {
    height: 1px;
    background-color: var(--harmoni-border-grey);
    margin: var(--harmoni-spacing-sm) 0;
}

.logout-btn {
    color: var(--harmoni-error);
}

.logout-btn:hover {
    background-color: rgba(244, 67, 54, 0.1);
}

.notification-btn {
    position: relative;
    padding: var(--harmoni-spacing-sm);
    border: none;
    background: none;
    cursor: pointer;
    border-radius: var(--harmoni-radius-md);
    transition: var(--harmoni-transition);
}

.notification-btn:hover {
    background-color: var(--harmoni-soft-grey);
}

.notification-icon {
    font-size: 20px;
}

.notification-badge {
    position: absolute;
    top: 4px;
    right: 4px;
    background-color: var(--harmoni-error);
    color: var(--harmoni-white);
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

.language-toggle {
    display: flex;
    align-items: center;
    gap: var(--harmoni-spacing-xs);
    padding: var(--harmoni-spacing-sm) var(--harmoni-spacing-md);
    border: 1px solid var(--harmoni-border-grey);
    background-color: var(--harmoni-white);
    cursor: pointer;
    border-radius: var(--harmoni-radius-md);
    transition: var(--harmoni-transition);
    font-family: var(--harmoni-font-primary);
    font-size: 12px;
    font-weight: 500;
}

.language-toggle:hover {
    border-color: var(--harmoni-teal-primary);
    background-color: rgba(0, 151, 167, 0.05);
}

.language-icon {
    font-size: 14px;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 24px;
    height: 24px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.hamburger-line {
    width: 100%;
    height: 2px;
    background-color: var(--harmoni-charcoal);
    transition: var(--harmoni-transition);
}

/* Mobile Responsive */
@media (max-width: 1024px) {
    .navbar-container {
        flex-wrap: wrap;
    }
    
    .navbar-menu {
        order: 3;
        flex-basis: 100%;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }
    
    .navbar-menu.menu-open {
        max-height: 500px;
    }
    
    .harmoni-navbar-nav {
        flex-direction: column;
        align-items: stretch;
        gap: 0;
        margin-top: var(--harmoni-spacing-md);
        border-top: 1px solid var(--harmoni-border-grey);
        padding-top: var(--harmoni-spacing-md);
    }
    
    .nav-item {
        width: 100%;
    }
    
    .harmoni-navbar-link {
        justify-content: space-between;
        width: 100%;
        padding: var(--harmoni-spacing-md);
    }
    
    .dropdown-menu {
        position: static;
        box-shadow: none;
        border: none;
        padding-left: var(--harmoni-spacing-lg);
        background-color: var(--harmoni-soft-grey);
        border-radius: 0;
        opacity: 1;
        visibility: visible;
        transform: none;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }
    
    .dropdown-menu.show {
        max-height: 300px;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .user-info {
        display: none;
    }
    
    .language-toggle .language-text {
        display: none;
    }
}

@media (max-width: 600px) {
    .navbar-actions {
        gap: var(--harmoni-spacing-sm);
    }
    
    .notification-btn,
    .language-toggle {
        padding: var(--harmoni-spacing-xs);
    }
    
    .user-avatar {
        width: 32px;
        height: 32px;
    }
}