
var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

// Add Authentication and Authorization
builder.Services.AddCascadingAuthenticationState();
builder.Services.AddScoped<HarmoniHSE360.BlazorServer.Services.CustomAuthenticationStateProvider>();
builder.Services.AddScoped<Microsoft.AspNetCore.Components.Authorization.AuthenticationStateProvider>(
    provider => provider.GetRequiredService<HarmoniHSE360.BlazorServer.Services.CustomAuthenticationStateProvider>());
builder.Services.AddScoped<HarmoniHSE360.BlazorServer.Services.AuthService>();
builder.Services.AddAuthentication()
    .AddCookie("Cookies", options =>
    {
        options.LoginPath = "/login";
        options.LogoutPath = "/logout";
        options.AccessDeniedPath = "/access-denied";
    });
builder.Services.AddAuthorization(options =>
{
    // Require authentication for all pages by default
    options.FallbackPolicy = new Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder()
        .RequireAuthenticatedUser()
        .Build();
});

// Add HttpClient for API calls
builder.Services.AddHttpClient("API", client =>
{
    var apiUrl = builder.Configuration["ApiBaseUrl"] ?? builder.Configuration["ApiUrl"] ?? "http://localhost:5000";
    client.BaseAddress = new Uri(apiUrl);
});

// Add Ant Design Blazor
builder.Services.AddAntDesign();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
}

// Use static files middleware
app.UseStaticFiles();

app.UseAuthentication();
app.UseAuthorization();

app.UseAntiforgery();

app.MapRazorComponents<HarmoniHSE360.BlazorServer.Components.App>()
    .AddInteractiveServerRenderMode();

app.Run();
