@page "/two-factor-auth"
@using Microsoft.AspNetCore.Components.Forms
@attribute [Microsoft.AspNetCore.Authorization.AllowAnonymous]
@rendermode InteractiveServer
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<PageTitle>Two-Factor Authentication - HarmoniHSE360</PageTitle>

<div class="twofa-container">
    <div class="twofa-wrapper">
        <Card class="twofa-card">
            <div class="brand-header">
                <div class="security-icon">
                    <Icon Type="safety-certificate" class="security-icon-large" />
                </div>
                <Title Level="2" class="security-title">Two-Factor Authentication</Title>
                <Text Type="@TextElementType.Secondary" class="security-subtitle">
                    Please complete verification to secure your account access
                </Text>
            </div>
            
            @if (currentStep == TwoFactorStep.MethodSelection)
            {
                <div class="method-selection">
                    <Title Level="4" Style="text-align: center; margin-bottom: 24px;">
                        Choose your verification method:
                    </Title>
                    
                    <Space Direction="DirectionVHType.Vertical" Size="@("large")" Style="width: 100%;">
                        <SpaceItem>
                            <Card class="@($"method-card{(selectedMethod == "sms" ? " method-selected" : "")}")"
                                  Hoverable="true"
                                  OnClick="@(() => SelectMethod("sms"))">
                                <div class="method-content">
                                    <Icon Type="mobile" class="method-icon" />
                                    <div class="method-details">
                                        <Text Strong>SMS Text Message</Text>
                                        <br />
                                        <Text Type="@TextElementType.Secondary">Send code to ***-***-@userPhone</Text>
                                    </div>
                                    @if (selectedMethod == "sms")
                                    {
                                        <Icon Type="check-circle" class="method-check" />
                                    }
                                </div>
                            </Card>
                        </SpaceItem>
                        
                        <SpaceItem>
                            <Card class="@($"method-card{(selectedMethod == "email" ? " method-selected" : "")}")"
                                  Hoverable="true"
                                  OnClick="@(() => SelectMethod("email"))">
                                <div class="method-content">
                                    <Icon Type="mail" class="method-icon" />
                                    <div class="method-details">
                                        <Text Strong>Email Verification</Text>
                                        <br />
                                        <Text Type="@TextElementType.Secondary">Send code to @maskEmail(userEmail)</Text>
                                    </div>
                                    @if (selectedMethod == "email")
                                    {
                                        <Icon Type="check-circle" class="method-check" />
                                    }
                                </div>
                            </Card>
                        </SpaceItem>
                        
                        <SpaceItem>
                            <Card class="@($"method-card{(selectedMethod == "app" ? " method-selected" : "")}")"
                                  Hoverable="true"
                                  OnClick="@(() => SelectMethod("app"))">
                                <div class="method-content">
                                    <Icon Type="key" class="method-icon" />
                                    <div class="method-details">
                                        <Text Strong>Authenticator App</Text>
                                        <br />
                                        <Text Type="@TextElementType.Secondary">Use your authenticator app</Text>
                                    </div>
                                    @if (selectedMethod == "app")
                                    {
                                        <Icon Type="check-circle" class="method-check" />
                                    }
                                </div>
                            </Card>
                        </SpaceItem>
                    </Space>
                    
                    <div class="method-actions">
                        <Space Size="@("middle")" Style="width: 100%; justify-content: center;">
                            <SpaceItem>
                                <Button Type="@ButtonType.Primary" 
                                        Size="@ButtonSize.Large"
                                        Loading="@isLoading"
                                        Disabled="@(string.IsNullOrEmpty(selectedMethod))"
                                        OnClick="@SendVerificationCode">
                                    Send Verification Code
                                </Button>
                            </SpaceItem>
                            <SpaceItem>
                                <Button Type="@ButtonType.Default" 
                                        Size="@ButtonSize.Large"
                                        OnClick="@GoBack">
                                    Back to Login
                                </Button>
                            </SpaceItem>
                        </Space>
                    </div>
                </div>
            }
            else if (currentStep == TwoFactorStep.CodeEntry)
            {
                <div class="code-entry">
                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <Alert Type="@AlertType.Error" 
                               Message="@errorMessage" 
                               ShowIcon="true" 
                               Closable="true"
                               OnClose="@(() => errorMessage = string.Empty)" 
                               Style="margin-bottom: 16px;" />
                    }
                    
                    @if (!string.IsNullOrEmpty(successMessage))
                    {
                        <Alert Type="@AlertType.Success" 
                               Message="@successMessage" 
                               ShowIcon="true" 
                               Closable="true"
                               OnClose="@(() => successMessage = string.Empty)" 
                               Style="margin-bottom: 16px;" />
                    }
                    
                    <div class="code-instruction">
                        <Text class="instruction-text">
                            @GetInstructionText()
                        </Text>
                    </div>
                    
                    <Form Model="@codeModel" 
                          OnFinish="@VerifyCode" 
                          OnFinishFailed="@HandleCodeFailed"
                          Layout="@FormLayout.Vertical"
                          class="code-form">
                        
                        <FormItem>
                            <div class="code-input-group">
                                @for (int i = 0; i < 6; i++)
                                {
                                    int index = i;
                                    <Input @bind-Value="@codeDigits[index]"
                                           MaxLength="1"
                                           Size="@InputSize.Large"
                                           class="code-digit"
                                           @onkeydown="@((e) => HandleKeyDown(e, index))"
                                           @onfocus="@(() => SelectDigit(index))"
                                           id="@($"digit-{index}")" />
                                }
                            </div>
                        </FormItem>
                        
                        <div class="resend-section">
                            @if (canResend)
                            {
                                <Button Type="@ButtonType.Link" 
                                        Size="@ButtonSize.Small"
                                        OnClick="@ResendCode">
                                    Resend Code
                                </Button>
                            }
                            else
                            {
                                <span class="resend-countdown" style="color: var(--text-secondary);">
                                    Resend code in @resendCountdown seconds
                                </span>
                            }
                        </div>
                        
                        <FormItem>
                            <Space Direction="DirectionVHType.Vertical" Size="@("middle")" Style="width: 100%;">
                                <SpaceItem>
                                    <Button Type="@ButtonType.Primary" 
                                            HtmlType="submit"
                                            Size="@ButtonSize.Large"
                                            Block="true"
                                            Loading="@isLoading"
                                            Disabled="@(codeModel.VerificationCode.Length < 6)">
                                        Verify & Continue
                                    </Button>
                                </SpaceItem>
                                <SpaceItem>
                                    <Button Type="@ButtonType.Default" 
                                            Size="@ButtonSize.Large"
                                            Block="true"
                                            OnClick="@GoBackToMethods">
                                        Use Different Method
                                    </Button>
                                </SpaceItem>
                            </Space>
                        </FormItem>
                    </Form>
                </div>
            }
        </Card>
        
        <!-- Security Information Sidebar -->
        <Card class="security-info-card" Size="@("small")">
            <Title Level="4" class="info-title">
                <Icon Type="shield-check" class="info-icon" />
                Why Two-Factor Authentication?
            </Title>
            
            <Space Direction="DirectionVHType.Vertical" Size="@("middle")" Style="width: 100%;">
                <SpaceItem>
                    <div class="security-benefit">
                        <Icon Type="lock" class="benefit-icon" />
                        <div class="benefit-content">
                            <Text Strong>Enhanced Security</Text>
                            <br />
                            <Text Type="@TextElementType.Secondary" class="benefit-description">
                                Protects your account even if your password is compromised.
                            </Text>
                        </div>
                    </div>
                </SpaceItem>
                
                <SpaceItem>
                    <div class="security-benefit">
                        <Icon Type="safety-certificate" class="benefit-icon" />
                        <div class="benefit-content">
                            <Text Strong>Compliance</Text>
                            <br />
                            <Text Type="@TextElementType.Secondary" class="benefit-description">
                                Meets security requirements and industry standards.
                            </Text>
                        </div>
                    </div>
                </SpaceItem>
                
                <SpaceItem>
                    <div class="security-benefit">
                        <Icon Type="team" class="benefit-icon" />
                        <div class="benefit-content">
                            <Text Strong>Access Control</Text>
                            <br />
                            <Text Type="@TextElementType.Secondary" class="benefit-description">
                                Ensures only authorized personnel can access HSE data.
                            </Text>
                        </div>
                    </div>
                </SpaceItem>
            </Space>
            
            <Divider />
            
            <div class="help-section">
                <Title Level="5">Need Help?</Title>
                <Paragraph class="help-text">
                    Contact IT Support at 
                    <a href="mailto:<EMAIL>"><EMAIL></a> 
                    or call extension 2187 for assistance.
                </Paragraph>
            </div>
        </Card>
    </div>
</div>


@code {
    private enum TwoFactorStep
    {
        MethodSelection,
        CodeEntry
    }

    private TwoFactorStep currentStep = TwoFactorStep.MethodSelection;
    private string selectedMethod = string.Empty;
    private string[] codeDigits = new string[6];
    private CodeModel codeModel = new();
    private bool isLoading = false;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool canResend = false;
    private int resendCountdown = 30;
    private System.Threading.Timer? countdownTimer;
    
    // Mock user data
    private string userEmail = "<EMAIL>";
    private string userPhone = "1234567890";

    protected override void OnInitialized()
    {
        // Initialize code digits
        for (int i = 0; i < codeDigits.Length; i++)
        {
            codeDigits[i] = string.Empty;
        }
    }

    private void SelectMethod(string method)
    {
        selectedMethod = method;
        errorMessage = string.Empty;
    }

    private async Task SendVerificationCode()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            
            // Simulate API call
            await Task.Delay(1500);
            
            successMessage = $"Verification code sent via {GetMethodDisplayName(selectedMethod)}";
            currentStep = TwoFactorStep.CodeEntry;
            StartResendTimer();
        }
        catch (Exception ex)
        {
            errorMessage = "Failed to send verification code. Please try again.";
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task VerifyCode(EditContext editContext)
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            
            codeModel.VerificationCode = string.Join("", codeDigits);
            
            // Simulate API call
            await Task.Delay(1000);
            
            // TODO: Implement actual verification logic
            if (codeModel.VerificationCode == "123456")
            {
                successMessage = "Verification successful! Redirecting...";
                await Task.Delay(1000);
                Navigation.NavigateTo("/dashboard");
            }
            else
            {
                errorMessage = "Invalid verification code. Please try again.";
                ClearCode();
            }
        }
        catch (Exception ex)
        {
            errorMessage = "Verification failed. Please try again.";
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task HandleCodeFailed(EditContext editContext)
    {
        errorMessage = "Please enter a valid 6-digit code.";
        await InvokeAsync(StateHasChanged);
    }

    private async Task ResendCode()
    {
        canResend = false;
        resendCountdown = 30;
        await SendVerificationCode();
        StartResendTimer();
    }

    private void StartResendTimer()
    {
        countdownTimer?.Dispose();
        countdownTimer = new System.Threading.Timer(async _ =>
        {
            resendCountdown--;
            if (resendCountdown <= 0)
            {
                canResend = true;
                countdownTimer?.Dispose();
            }
            await InvokeAsync(StateHasChanged);
        }, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
    }

    private async Task HandleKeyDown(KeyboardEventArgs e, int index)
    {
        if (e.Key == "Backspace")
        {
            if (string.IsNullOrEmpty(codeDigits[index]) && index > 0)
            {
                // Move to previous digit
                await JSRuntime.InvokeVoidAsync("harmoni.focusElement", $"digit-{index - 1}");
            }
            else
            {
                codeDigits[index] = string.Empty;
            }
        }
        else if (e.Key.Length == 1 && char.IsDigit(e.Key[0]))
        {
            codeDigits[index] = e.Key;
            if (index < 5)
            {
                // Move to next digit
                await JSRuntime.InvokeVoidAsync("harmoni.focusElement", $"digit-{index + 1}");
            }
        }
        
        codeModel.VerificationCode = string.Join("", codeDigits);
        StateHasChanged();
    }

    private async Task SelectDigit(int index)
    {
        await JSRuntime.InvokeVoidAsync("harmoni.selectText", $"digit-{index}");
    }

    private void ClearCode()
    {
        for (int i = 0; i < codeDigits.Length; i++)
        {
            codeDigits[i] = string.Empty;
        }
        codeModel.VerificationCode = string.Empty;
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/login");
    }

    private void GoBackToMethods()
    {
        currentStep = TwoFactorStep.MethodSelection;
        selectedMethod = string.Empty;
        errorMessage = string.Empty;
        successMessage = string.Empty;
        ClearCode();
        countdownTimer?.Dispose();
    }

    private string GetMethodDisplayName(string method)
    {
        return method switch
        {
            "sms" => "SMS",
            "email" => "email",
            "app" => "authenticator app",
            _ => "selected method"
        };
    }

    private string GetInstructionText()
    {
        return selectedMethod switch
        {
            "sms" => $"Enter the 6-digit code sent to your phone number ending in {userPhone.Substring(userPhone.Length - 4)}",
            "email" => $"Enter the 6-digit code sent to {maskEmail(userEmail)}",
            "app" => "Enter the 6-digit code from your authenticator app",
            _ => "Enter the 6-digit verification code"
        };
    }

    private string maskEmail(string email)
    {
        if (string.IsNullOrEmpty(email) || !email.Contains('@'))
            return email;

        var parts = email.Split('@');
        var username = parts[0];
        var domain = parts[1];

        if (username.Length <= 2)
            return $"{username[0]}***@{domain}";

        return $"{username.Substring(0, 2)}***@{domain}";
    }

    public void Dispose()
    {
        countdownTimer?.Dispose();
    }

    public class CodeModel
    {
        [Required(ErrorMessage = "Verification code is required")]
        [StringLength(6, MinimumLength = 6, ErrorMessage = "Verification code must be 6 digits")]
        public string VerificationCode { get; set; } = string.Empty;
    }
}