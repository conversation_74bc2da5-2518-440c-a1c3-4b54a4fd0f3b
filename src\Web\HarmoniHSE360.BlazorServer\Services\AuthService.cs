using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace HarmoniHSE360.BlazorServer.Services;

public class AuthService
{
    private readonly HttpClient _httpClient;
    private readonly CustomAuthenticationStateProvider _authStateProvider;
    private readonly IJSRuntime _jsRuntime;

    public AuthService(IHttpClientFactory httpClientFactory, AuthenticationStateProvider authStateProvider, IJSRuntime jsRuntime)
    {
        _httpClient = httpClientFactory.CreateClient("API");
        _authStateProvider = (CustomAuthenticationStateProvider)authStateProvider;
        _jsRuntime = jsRuntime;
    }

    public async Task<LoginResult> LoginAsync(string email, string password)
    {
        try
        {
            var loginRequest = new
            {
                Email = email,
                Password = password
            };

            var response = await _httpClient.PostAsJsonAsync("api/auth/login", loginRequest);
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<ApiLoginResponse>(content, new JsonSerializerOptions 
                { 
                    PropertyNameCaseInsensitive = true 
                });
                
                if (result != null && !string.IsNullOrEmpty(result.AccessToken))
                {
                    // Store tokens securely
                    await StoreTokensAsync(result.AccessToken, result.RefreshToken);
                    
                    // Parse JWT token to get user claims
                    var tokenHandler = new JwtSecurityTokenHandler();
                    var jwtToken = tokenHandler.ReadJwtToken(result.AccessToken);
                    
                    var userIdClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
                    var emailClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)?.Value;
                    var firstNameClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == ClaimTypes.GivenName)?.Value;
                    var lastNameClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Surname)?.Value;
                    var rolesClaims = jwtToken.Claims.Where(c => c.Type == ClaimTypes.Role).Select(c => c.Value).ToList();
                    
                    // Mark user as authenticated in our custom auth state provider
                    _authStateProvider.MarkUserAsAuthenticated(
                        emailClaim ?? email,
                        userIdClaim ?? "unknown",
                        firstNameClaim ?? "User",
                        lastNameClaim ?? "",
                        rolesClaims
                    );
                    
                    return new LoginResult 
                    { 
                        Success = true,
                        RequiresTwoFactor = result.RequiresTwoFactor,
                        AccessToken = result.AccessToken,
                        ExpiresAt = result.ExpiresAt
                    };
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                var errorMessage = "Invalid email or password";
                
                try
                {
                    var errorResponse = JsonSerializer.Deserialize<ErrorResponse>(errorContent, new JsonSerializerOptions 
                    { 
                        PropertyNameCaseInsensitive = true 
                    });
                    if (!string.IsNullOrEmpty(errorResponse?.Message))
                    {
                        errorMessage = errorResponse.Message;
                    }
                }
                catch
                {
                    // Use default error message if JSON parsing fails
                }
                
                return new LoginResult 
                { 
                    Success = false, 
                    ErrorMessage = errorMessage
                };
            }
            
            return new LoginResult 
            { 
                Success = false, 
                ErrorMessage = "Login failed. Please try again." 
            };
        }
        catch (HttpRequestException httpEx)
        {
            return new LoginResult 
            { 
                Success = false, 
                ErrorMessage = "Unable to connect to the server. Please check your internet connection and try again."
            };
        }
        catch (Exception ex)
        {
            return new LoginResult 
            { 
                Success = false, 
                ErrorMessage = "An unexpected error occurred. Please try again."
            };
        }
    }

    public async Task<RegisterResult> RegisterAsync(RegisterRequest registerRequest)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("api/auth/register", registerRequest);
            
            if (response.IsSuccessStatusCode)
            {
                return new RegisterResult { Success = true };
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                var errorMessage = "Registration failed. Please try again.";
                
                try
                {
                    var errorResponse = JsonSerializer.Deserialize<ErrorResponse>(errorContent, new JsonSerializerOptions 
                    { 
                        PropertyNameCaseInsensitive = true 
                    });
                    if (!string.IsNullOrEmpty(errorResponse?.Message))
                    {
                        errorMessage = errorResponse.Message;
                    }
                }
                catch
                {
                    // Use default error message if JSON parsing fails
                }
                
                return new RegisterResult 
                { 
                    Success = false, 
                    ErrorMessage = errorMessage
                };
            }
        }
        catch (Exception ex)
        {
            return new RegisterResult 
            { 
                Success = false, 
                ErrorMessage = "An error occurred during registration. Please try again."
            };
        }
    }

    public async Task LogoutAsync()
    {
        // Clear stored tokens
        await ClearTokensAsync();
        
        // Mark user as logged out
        _authStateProvider.MarkUserAsLoggedOut();
    }

    public async Task<string?> GetAccessTokenAsync()
    {
        try
        {
            return await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", "harmoni_access_token");
        }
        catch
        {
            return null;
        }
    }

    private async Task StoreTokensAsync(string accessToken, string refreshToken)
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "harmoni_access_token", accessToken);
            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "harmoni_refresh_token", refreshToken);
        }
        catch
        {
            // Handle storage errors gracefully
        }
    }

    private async Task ClearTokensAsync()
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "harmoni_access_token");
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "harmoni_refresh_token");
        }
        catch
        {
            // Handle storage errors gracefully
        }
    }
}

public class LoginResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public bool RequiresTwoFactor { get; set; }
    public string? AccessToken { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

public class RegisterResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
}

public class ApiLoginResponse
{
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
    public bool RequiresTwoFactor { get; set; }
}

public class RegisterRequest
{
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string AuthenticationSource { get; set; } = "Local";
    public string? ExternalId { get; set; }
    public string Department { get; set; } = string.Empty;
    public string Position { get; set; } = string.Empty;
}

public class ErrorResponse
{
    public string? Message { get; set; }
    public string? Detail { get; set; }
}