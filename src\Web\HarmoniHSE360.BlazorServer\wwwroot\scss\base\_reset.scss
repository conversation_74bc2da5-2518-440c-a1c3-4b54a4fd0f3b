// HarmoniHSE360 CSS Reset
// Modern CSS reset with Blazor-specific considerations

// ============================================
// Box Sizing Reset
// ============================================
*,
*::before,
*::after {
  box-sizing: border-box;
}

// ============================================
// Document Reset
// ============================================
html {
  // Prevent font size adjustments after orientation changes
  -webkit-text-size-adjust: 100%;
  
  // Smooth scrolling for anchor links
  scroll-behavior: smooth;
  
  // Better font rendering
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// ============================================
// Body Reset
// ============================================
body {
  margin: 0;
  padding: 0;
  font-family: $harmoni-font-primary;
  font-size: $harmoni-font-size-base;
  line-height: $harmoni-line-height-normal;
  color: $harmoni-charcoal;
  background-color: $harmoni-soft-white;
  
  // Prevent horizontal scroll
  overflow-x: hidden;
}

// ============================================
// Typography Reset
// ============================================
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 $harmoni-spacing-md 0;
  font-family: $harmoni-font-secondary;
  font-weight: $harmoni-font-weight-semibold;
  line-height: $harmoni-line-height-tight;
  color: $harmoni-charcoal;
}

p {
  margin: 0 0 $harmoni-spacing-md 0;
}

// ============================================
// List Reset
// ============================================
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

// ============================================
// Link Reset
// ============================================
a {
  color: $harmoni-teal-primary;
  text-decoration: none;
  transition: color $harmoni-transition;
  
  &:hover {
    color: $harmoni-deep-blue;
    text-decoration: underline;
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba($harmoni-teal-primary, 0.3);
  }
}

// ============================================
// Form Reset
// ============================================
button,
input,
optgroup,
select,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
  
  &:disabled {
    cursor: not-allowed;
  }
}

// ============================================
// Image Reset
// ============================================
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

// ============================================
// Table Reset
// ============================================
table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

// ============================================
// Blazor-Specific Reset
// ============================================
// Remove default Blazor error UI styles
#blazor-error-ui {
  background: $harmoni-error;
  color: white;
  padding: $harmoni-spacing-md;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: z(tooltip);
  display: none;
  
  a {
    color: white;
    text-decoration: underline;
    
    &:hover {
      color: $harmoni-soft-white;
    }
  }
}

// Blazor loading progress
.loading-progress {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, $harmoni-teal-primary, $harmoni-deep-blue);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.5s ease-out;
  z-index: z(tooltip);
}

// ============================================
// Accessibility
// ============================================
// Skip to main content link
.skip-to-main {
  position: absolute;
  left: -9999px;
  top: $harmoni-spacing-sm;
  z-index: z(tooltip);
  
  &:focus {
    left: $harmoni-spacing-sm;
    background: $harmoni-deep-blue;
    color: white;
    padding: $harmoni-spacing-sm $harmoni-spacing-md;
    border-radius: $harmoni-radius-md;
    text-decoration: none;
  }
}

// Screen reader only content
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}