using HarmoniHSE360.BuildingBlocks.Application.Queries;

namespace HarmoniHSE360.Modules.UserManagement.Application.Queries.GetUser;

public sealed record GetUserQuery(Guid UserId) : IQuery<UserDto>;

public sealed record UserDto(
    Guid Id,
    string Email,
    string FirstName,
    string LastName,
    string? PhoneNumber,
    string? Department,
    string? Position,
    bool IsActive,
    bool EmailConfirmed,
    bool TwoFactorEnabled,
    DateTime CreatedAt,
    DateTime? LastLoginAt,
    IReadOnlyList<UserRoleDto> Roles
);

public sealed record UserRoleDto(
    Guid RoleId,
    string RoleName,
    string? RoleDescription
);