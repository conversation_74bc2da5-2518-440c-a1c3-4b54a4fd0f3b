using HarmoniHSE360.Modules.UserManagement.Domain.Users;

namespace HarmoniHSE360.Modules.UserManagement.Infrastructure.Persistence.DataSeeding;

public static class DefaultPermissions
{
    public static List<Permission> GetAllPermissions()
    {
        return new List<Permission>
        {
            // IncidentManagement Permissions
            Permission.Create(Permission.Modules.IncidentManagement, "Incident", Permission.Actions.View, "View incident reports"),
            Permission.Create(Permission.Modules.IncidentManagement, "Incident", Permission.Actions.Create, "Create new incident reports"),
            Permission.Create(Permission.Modules.IncidentManagement, "Incident", Permission.Actions.Edit, "Edit incident reports"),
            Permission.Create(Permission.Modules.IncidentManagement, "Incident", Permission.Actions.Delete, "Delete incident reports"),
            Permission.Create(Permission.Modules.IncidentManagement, "Incident", Permission.Actions.Approve, "Approve incident reports"),
            Permission.Create(Permission.Modules.IncidentManagement, "Incident", Permission.Actions.Export, "Export incident data"),
            Permission.Create(Permission.Modules.IncidentManagement, "Investigation", Permission.Actions.View, "View incident investigations"),
            Permission.Create(Permission.Modules.IncidentManagement, "Investigation", Permission.Actions.Create, "Create incident investigations"),
            Permission.Create(Permission.Modules.IncidentManagement, "Investigation", Permission.Actions.Edit, "Edit incident investigations"),
            Permission.Create(Permission.Modules.IncidentManagement, "Investigation", Permission.Actions.Approve, "Approve incident investigations"),

            // HazardReporting Permissions
            Permission.Create(Permission.Modules.HazardReporting, "Hazard", Permission.Actions.View, "View hazard reports"),
            Permission.Create(Permission.Modules.HazardReporting, "Hazard", Permission.Actions.Create, "Create hazard reports"),
            Permission.Create(Permission.Modules.HazardReporting, "Hazard", Permission.Actions.Edit, "Edit hazard reports"),
            Permission.Create(Permission.Modules.HazardReporting, "Hazard", Permission.Actions.Delete, "Delete hazard reports"),
            Permission.Create(Permission.Modules.HazardReporting, "Hazard", Permission.Actions.Approve, "Approve hazard mitigation plans"),
            Permission.Create(Permission.Modules.HazardReporting, "Hazard", Permission.Actions.Export, "Export hazard data"),
            Permission.Create(Permission.Modules.HazardReporting, "RiskAssessment", Permission.Actions.View, "View risk assessments"),
            Permission.Create(Permission.Modules.HazardReporting, "RiskAssessment", Permission.Actions.Create, "Create risk assessments"),
            Permission.Create(Permission.Modules.HazardReporting, "RiskAssessment", Permission.Actions.Edit, "Edit risk assessments"),
            Permission.Create(Permission.Modules.HazardReporting, "RiskAssessment", Permission.Actions.Approve, "Approve risk assessments"),

            // ComplianceAudit Permissions
            Permission.Create(Permission.Modules.ComplianceAudit, "Audit", Permission.Actions.View, "View compliance audits"),
            Permission.Create(Permission.Modules.ComplianceAudit, "Audit", Permission.Actions.Create, "Create compliance audits"),
            Permission.Create(Permission.Modules.ComplianceAudit, "Audit", Permission.Actions.Edit, "Edit compliance audits"),
            Permission.Create(Permission.Modules.ComplianceAudit, "Audit", Permission.Actions.Delete, "Delete compliance audits"),
            Permission.Create(Permission.Modules.ComplianceAudit, "Audit", Permission.Actions.Approve, "Approve audit findings"),
            Permission.Create(Permission.Modules.ComplianceAudit, "Audit", Permission.Actions.Export, "Export audit reports"),
            Permission.Create(Permission.Modules.ComplianceAudit, "Finding", Permission.Actions.View, "View audit findings"),
            Permission.Create(Permission.Modules.ComplianceAudit, "Finding", Permission.Actions.Create, "Create audit findings"),
            Permission.Create(Permission.Modules.ComplianceAudit, "Finding", Permission.Actions.Edit, "Edit audit findings"),
            Permission.Create(Permission.Modules.ComplianceAudit, "CorrectiveAction", Permission.Actions.View, "View corrective actions"),
            Permission.Create(Permission.Modules.ComplianceAudit, "CorrectiveAction", Permission.Actions.Create, "Create corrective actions"),
            Permission.Create(Permission.Modules.ComplianceAudit, "CorrectiveAction", Permission.Actions.Edit, "Edit corrective actions"),

            // DocumentManagement Permissions
            Permission.Create(Permission.Modules.DocumentManagement, "Document", Permission.Actions.View, "View documents"),
            Permission.Create(Permission.Modules.DocumentManagement, "Document", Permission.Actions.Create, "Upload documents"),
            Permission.Create(Permission.Modules.DocumentManagement, "Document", Permission.Actions.Edit, "Edit document metadata"),
            Permission.Create(Permission.Modules.DocumentManagement, "Document", Permission.Actions.Delete, "Delete documents"),
            Permission.Create(Permission.Modules.DocumentManagement, "Document", Permission.Actions.Approve, "Approve document revisions"),
            Permission.Create(Permission.Modules.DocumentManagement, "Template", Permission.Actions.View, "View document templates"),
            Permission.Create(Permission.Modules.DocumentManagement, "Template", Permission.Actions.Create, "Create document templates"),
            Permission.Create(Permission.Modules.DocumentManagement, "Template", Permission.Actions.Edit, "Edit document templates"),

            // PermitToWork Permissions
            Permission.Create(Permission.Modules.PermitToWork, "Permit", Permission.Actions.View, "View work permits"),
            Permission.Create(Permission.Modules.PermitToWork, "Permit", Permission.Actions.Create, "Create work permits"),
            Permission.Create(Permission.Modules.PermitToWork, "Permit", Permission.Actions.Edit, "Edit work permits"),
            Permission.Create(Permission.Modules.PermitToWork, "Permit", Permission.Actions.Delete, "Delete work permits"),
            Permission.Create(Permission.Modules.PermitToWork, "Permit", Permission.Actions.Approve, "Approve work permits"),
            Permission.Create(Permission.Modules.PermitToWork, "Permit", Permission.Actions.Export, "Export permit reports"),
            Permission.Create(Permission.Modules.PermitToWork, "Isolation", Permission.Actions.View, "View isolation procedures"),
            Permission.Create(Permission.Modules.PermitToWork, "Isolation", Permission.Actions.Create, "Create isolation procedures"),
            Permission.Create(Permission.Modules.PermitToWork, "Isolation", Permission.Actions.Edit, "Edit isolation procedures"),

            // Training Permissions
            Permission.Create(Permission.Modules.Training, "Course", Permission.Actions.View, "View training courses"),
            Permission.Create(Permission.Modules.Training, "Course", Permission.Actions.Create, "Create training courses"),
            Permission.Create(Permission.Modules.Training, "Course", Permission.Actions.Edit, "Edit training courses"),
            Permission.Create(Permission.Modules.Training, "Course", Permission.Actions.Delete, "Delete training courses"),
            Permission.Create(Permission.Modules.Training, "Enrollment", Permission.Actions.View, "View training enrollments"),
            Permission.Create(Permission.Modules.Training, "Enrollment", Permission.Actions.Create, "Enroll users in training"),
            Permission.Create(Permission.Modules.Training, "Enrollment", Permission.Actions.Edit, "Edit training enrollments"),
            Permission.Create(Permission.Modules.Training, "Record", Permission.Actions.View, "View training records"),
            Permission.Create(Permission.Modules.Training, "Record", Permission.Actions.Export, "Export training records"),
            Permission.Create(Permission.Modules.Training, "Certification", Permission.Actions.View, "View certifications"),
            Permission.Create(Permission.Modules.Training, "Certification", Permission.Actions.Create, "Issue certifications"),
            Permission.Create(Permission.Modules.Training, "Certification", Permission.Actions.Edit, "Edit certifications"),

            // Environmental Permissions
            Permission.Create(Permission.Modules.Environmental, "Monitoring", Permission.Actions.View, "View environmental monitoring data"),
            Permission.Create(Permission.Modules.Environmental, "Monitoring", Permission.Actions.Create, "Create monitoring records"),
            Permission.Create(Permission.Modules.Environmental, "Monitoring", Permission.Actions.Edit, "Edit monitoring records"),
            Permission.Create(Permission.Modules.Environmental, "Monitoring", Permission.Actions.Export, "Export environmental data"),
            Permission.Create(Permission.Modules.Environmental, "WasteManagement", Permission.Actions.View, "View waste management records"),
            Permission.Create(Permission.Modules.Environmental, "WasteManagement", Permission.Actions.Create, "Create waste management records"),
            Permission.Create(Permission.Modules.Environmental, "WasteManagement", Permission.Actions.Edit, "Edit waste management records"),
            Permission.Create(Permission.Modules.Environmental, "EmissionTracking", Permission.Actions.View, "View emission tracking"),
            Permission.Create(Permission.Modules.Environmental, "EmissionTracking", Permission.Actions.Create, "Create emission records"),
            Permission.Create(Permission.Modules.Environmental, "EmissionTracking", Permission.Actions.Edit, "Edit emission records"),

            // Analytics Permissions
            Permission.Create(Permission.Modules.Analytics, "Dashboard", Permission.Actions.View, "View analytics dashboards"),
            Permission.Create(Permission.Modules.Analytics, "Report", Permission.Actions.View, "View analytics reports"),
            Permission.Create(Permission.Modules.Analytics, "Report", Permission.Actions.Create, "Create custom reports"),
            Permission.Create(Permission.Modules.Analytics, "Report", Permission.Actions.Export, "Export analytics data"),
            Permission.Create(Permission.Modules.Analytics, "KPI", Permission.Actions.View, "View KPI metrics"),
            Permission.Create(Permission.Modules.Analytics, "KPI", Permission.Actions.Configure, "Configure KPI metrics"),
            Permission.Create(Permission.Modules.Analytics, "Trend", Permission.Actions.View, "View trend analysis"),

            // UserManagement Permissions
            Permission.Create(Permission.Modules.UserManagement, "User", Permission.Actions.View, "View user accounts"),
            Permission.Create(Permission.Modules.UserManagement, "User", Permission.Actions.Create, "Create user accounts"),
            Permission.Create(Permission.Modules.UserManagement, "User", Permission.Actions.Edit, "Edit user accounts"),
            Permission.Create(Permission.Modules.UserManagement, "User", Permission.Actions.Delete, "Delete user accounts"),
            Permission.Create(Permission.Modules.UserManagement, "Role", Permission.Actions.View, "View roles"),
            Permission.Create(Permission.Modules.UserManagement, "Role", Permission.Actions.Create, "Create roles"),
            Permission.Create(Permission.Modules.UserManagement, "Role", Permission.Actions.Edit, "Edit roles"),
            Permission.Create(Permission.Modules.UserManagement, "Role", Permission.Actions.Delete, "Delete roles"),
            Permission.Create(Permission.Modules.UserManagement, "Permission", Permission.Actions.View, "View permissions"),
            Permission.Create(Permission.Modules.UserManagement, "Permission", Permission.Actions.Configure, "Configure permissions"),

            // System Permissions
            Permission.Create(Permission.Modules.System, "Configuration", Permission.Actions.View, "View system configuration"),
            Permission.Create(Permission.Modules.System, "Configuration", Permission.Actions.Configure, "Configure system settings"),
            Permission.Create(Permission.Modules.System, "AuditLog", Permission.Actions.View, "View audit logs"),
            Permission.Create(Permission.Modules.System, "AuditLog", Permission.Actions.Export, "Export audit logs"),
            Permission.Create(Permission.Modules.System, "Backup", Permission.Actions.View, "View backup status"),
            Permission.Create(Permission.Modules.System, "Backup", Permission.Actions.Create, "Create system backups"),
            Permission.Create(Permission.Modules.System, "Integration", Permission.Actions.View, "View system integrations"),
            Permission.Create(Permission.Modules.System, "Integration", Permission.Actions.Configure, "Configure system integrations"),
            Permission.Create(Permission.Modules.System, "Notification", Permission.Actions.View, "View notification settings"),
            Permission.Create(Permission.Modules.System, "Notification", Permission.Actions.Configure, "Configure notifications")
        };
    }

    public static Dictionary<string, List<Permission>> GetRolePermissions()
    {
        var allPermissions = GetAllPermissions();
        var rolePermissions = new Dictionary<string, List<Permission>>();

        // SystemAdministrator - Full access to everything
        rolePermissions["SystemAdministrator"] = allPermissions.ToList();

        // HSEManager - Comprehensive HSE functionality access
        rolePermissions["HSEManager"] = allPermissions.Where(p =>
            p.Module == Permission.Modules.IncidentManagement ||
            p.Module == Permission.Modules.HazardReporting ||
            p.Module == Permission.Modules.ComplianceAudit ||
            p.Module == Permission.Modules.DocumentManagement ||
            p.Module == Permission.Modules.PermitToWork ||
            p.Module == Permission.Modules.Training ||
            p.Module == Permission.Modules.Environmental ||
            p.Module == Permission.Modules.Analytics ||
            (p.Module == Permission.Modules.UserManagement && p.Action != Permission.Actions.Delete) ||
            (p.Module == Permission.Modules.System && p.Resource == "AuditLog")
        ).ToList();

        // DepartmentHead - Department-specific management features
        rolePermissions["DepartmentHead"] = allPermissions.Where(p =>
            (p.Module == Permission.Modules.IncidentManagement && p.Action != Permission.Actions.Delete) ||
            (p.Module == Permission.Modules.HazardReporting && p.Action != Permission.Actions.Delete) ||
            (p.Module == Permission.Modules.ComplianceAudit && p.Action == Permission.Actions.View) ||
            (p.Module == Permission.Modules.DocumentManagement && p.Action != Permission.Actions.Delete) ||
            (p.Module == Permission.Modules.PermitToWork && p.Action != Permission.Actions.Delete) ||
            (p.Module == Permission.Modules.Training) ||
            (p.Module == Permission.Modules.Environmental && p.Action != Permission.Actions.Delete) ||
            (p.Module == Permission.Modules.Analytics && p.Action == Permission.Actions.View) ||
            (p.Module == Permission.Modules.UserManagement && p.Action == Permission.Actions.View)
        ).ToList();

        // Employee - Standard HSE participation features
        rolePermissions["Employee"] = allPermissions.Where(p =>
            (p.Module == Permission.Modules.IncidentManagement && (p.Action == Permission.Actions.View || p.Action == Permission.Actions.Create)) ||
            (p.Module == Permission.Modules.HazardReporting && (p.Action == Permission.Actions.View || p.Action == Permission.Actions.Create)) ||
            (p.Module == Permission.Modules.DocumentManagement && p.Action == Permission.Actions.View) ||
            (p.Module == Permission.Modules.PermitToWork && (p.Action == Permission.Actions.View || p.Action == Permission.Actions.Create)) ||
            (p.Module == Permission.Modules.Training && (p.Action == Permission.Actions.View || p.Resource == "Enrollment")) ||
            (p.Module == Permission.Modules.Environmental && p.Action == Permission.Actions.View)
        ).ToList();

        // Contractor - Limited access for relevant HSE activities
        rolePermissions["Contractor"] = allPermissions.Where(p =>
            (p.Module == Permission.Modules.IncidentManagement && (p.Action == Permission.Actions.View || p.Action == Permission.Actions.Create)) ||
            (p.Module == Permission.Modules.HazardReporting && (p.Action == Permission.Actions.View || p.Action == Permission.Actions.Create)) ||
            (p.Module == Permission.Modules.DocumentManagement && p.Action == Permission.Actions.View) ||
            (p.Module == Permission.Modules.PermitToWork && p.Action == Permission.Actions.View) ||
            (p.Module == Permission.Modules.Training && p.Action == Permission.Actions.View)
        ).ToList();

        // Student - Restricted access for hazard reporting
        rolePermissions["Student"] = allPermissions.Where(p =>
            (p.Module == Permission.Modules.HazardReporting && (p.Action == Permission.Actions.View || p.Action == Permission.Actions.Create)) ||
            (p.Module == Permission.Modules.Training && p.Action == Permission.Actions.View)
        ).ToList();

        // Parent - View-only access for incident notifications
        rolePermissions["Parent"] = allPermissions.Where(p =>
            (p.Module == Permission.Modules.IncidentManagement && p.Action == Permission.Actions.View) ||
            (p.Module == Permission.Modules.HazardReporting && p.Action == Permission.Actions.View)
        ).ToList();

        return rolePermissions;
    }
}