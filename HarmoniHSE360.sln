﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{4BA95578-6B9D-45F7-8856-5521CC36E904}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "BuildingBlocks", "BuildingBlocks", "{C80B8894-E742-4941-BB2E-BD4A414293A0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HarmoniHSE360.BuildingBlocks.Domain", "src\BuildingBlocks\HarmoniHSE360.BuildingBlocks.Domain\HarmoniHSE360.BuildingBlocks.Domain.csproj", "{370DE14C-CE10-48D0-A7B9-7306B9606C13}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HarmoniHSE360.BuildingBlocks.Application", "src\BuildingBlocks\HarmoniHSE360.BuildingBlocks.Application\HarmoniHSE360.BuildingBlocks.Application.csproj", "{2FF87C63-A123-4585-9CB5-E8A19A600359}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HarmoniHSE360.BuildingBlocks.Infrastructure", "src\BuildingBlocks\HarmoniHSE360.BuildingBlocks.Infrastructure\HarmoniHSE360.BuildingBlocks.Infrastructure.csproj", "{4D9BBC75-39EA-4D1F-834F-D9445E64440A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HarmoniHSE360.BuildingBlocks.EventBus", "src\BuildingBlocks\HarmoniHSE360.BuildingBlocks.EventBus\HarmoniHSE360.BuildingBlocks.EventBus.csproj", "{F1488F85-1BF2-427A-B3C9-BD1F98C93A99}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "API", "API", "{184D92E1-FBE5-49D0-BDA8-9195DDF01840}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HarmoniHSE360.Api", "src\API\HarmoniHSE360.Api\HarmoniHSE360.Api.csproj", "{EE1E40BD-2C9C-4481-B522-A90F490E0C3F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Web", "Web", "{B1A10047-3B18-475B-BF26-B7CE41E28777}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HarmoniHSE360.BlazorServer", "src\Web\HarmoniHSE360.BlazorServer\HarmoniHSE360.BlazorServer.csproj", "{F0A2210F-4D23-42A1-8FE9-0750CB7C11AB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Modules", "Modules", "{81B57710-00BB-42E8-B20E-CA308A90D42A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UserManagement", "UserManagement", "{EA889B6D-06F7-40F9-8362-9EA59D43689A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HarmoniHSE360.Modules.UserManagement.Domain", "src\Modules\UserManagement\HarmoniHSE360.Modules.UserManagement.Domain\HarmoniHSE360.Modules.UserManagement.Domain.csproj", "{88A3C8AB-F01C-419A-B148-B1D74F8DABB1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HarmoniHSE360.Modules.UserManagement.Application", "src\Modules\UserManagement\HarmoniHSE360.Modules.UserManagement.Application\HarmoniHSE360.Modules.UserManagement.Application.csproj", "{7240AFE9-E686-434B-B6C2-732F8840B357}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HarmoniHSE360.Modules.UserManagement.Infrastructure", "src\Modules\UserManagement\HarmoniHSE360.Modules.UserManagement.Infrastructure\HarmoniHSE360.Modules.UserManagement.Infrastructure.csproj", "{AAC81072-3A68-46FE-B324-B797A1E1AB5A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HarmoniHSE360.Modules.UserManagement.Api", "src\Modules\UserManagement\HarmoniHSE360.Modules.UserManagement.Api\HarmoniHSE360.Modules.UserManagement.Api.csproj", "{B10A56BE-6A33-46D2-B07D-8323A8162F1B}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{370DE14C-CE10-48D0-A7B9-7306B9606C13}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{370DE14C-CE10-48D0-A7B9-7306B9606C13}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{370DE14C-CE10-48D0-A7B9-7306B9606C13}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{370DE14C-CE10-48D0-A7B9-7306B9606C13}.Release|Any CPU.Build.0 = Release|Any CPU
		{2FF87C63-A123-4585-9CB5-E8A19A600359}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2FF87C63-A123-4585-9CB5-E8A19A600359}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2FF87C63-A123-4585-9CB5-E8A19A600359}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2FF87C63-A123-4585-9CB5-E8A19A600359}.Release|Any CPU.Build.0 = Release|Any CPU
		{4D9BBC75-39EA-4D1F-834F-D9445E64440A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4D9BBC75-39EA-4D1F-834F-D9445E64440A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4D9BBC75-39EA-4D1F-834F-D9445E64440A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4D9BBC75-39EA-4D1F-834F-D9445E64440A}.Release|Any CPU.Build.0 = Release|Any CPU
		{F1488F85-1BF2-427A-B3C9-BD1F98C93A99}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F1488F85-1BF2-427A-B3C9-BD1F98C93A99}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F1488F85-1BF2-427A-B3C9-BD1F98C93A99}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F1488F85-1BF2-427A-B3C9-BD1F98C93A99}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE1E40BD-2C9C-4481-B522-A90F490E0C3F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE1E40BD-2C9C-4481-B522-A90F490E0C3F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE1E40BD-2C9C-4481-B522-A90F490E0C3F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE1E40BD-2C9C-4481-B522-A90F490E0C3F}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0A2210F-4D23-42A1-8FE9-0750CB7C11AB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0A2210F-4D23-42A1-8FE9-0750CB7C11AB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0A2210F-4D23-42A1-8FE9-0750CB7C11AB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0A2210F-4D23-42A1-8FE9-0750CB7C11AB}.Release|Any CPU.Build.0 = Release|Any CPU
		{88A3C8AB-F01C-419A-B148-B1D74F8DABB1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{88A3C8AB-F01C-419A-B148-B1D74F8DABB1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{88A3C8AB-F01C-419A-B148-B1D74F8DABB1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{88A3C8AB-F01C-419A-B148-B1D74F8DABB1}.Release|Any CPU.Build.0 = Release|Any CPU
		{7240AFE9-E686-434B-B6C2-732F8840B357}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7240AFE9-E686-434B-B6C2-732F8840B357}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7240AFE9-E686-434B-B6C2-732F8840B357}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7240AFE9-E686-434B-B6C2-732F8840B357}.Release|Any CPU.Build.0 = Release|Any CPU
		{AAC81072-3A68-46FE-B324-B797A1E1AB5A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AAC81072-3A68-46FE-B324-B797A1E1AB5A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AAC81072-3A68-46FE-B324-B797A1E1AB5A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AAC81072-3A68-46FE-B324-B797A1E1AB5A}.Release|Any CPU.Build.0 = Release|Any CPU
		{B10A56BE-6A33-46D2-B07D-8323A8162F1B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B10A56BE-6A33-46D2-B07D-8323A8162F1B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B10A56BE-6A33-46D2-B07D-8323A8162F1B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B10A56BE-6A33-46D2-B07D-8323A8162F1B}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C80B8894-E742-4941-BB2E-BD4A414293A0} = {4BA95578-6B9D-45F7-8856-5521CC36E904}
		{370DE14C-CE10-48D0-A7B9-7306B9606C13} = {C80B8894-E742-4941-BB2E-BD4A414293A0}
		{2FF87C63-A123-4585-9CB5-E8A19A600359} = {C80B8894-E742-4941-BB2E-BD4A414293A0}
		{4D9BBC75-39EA-4D1F-834F-D9445E64440A} = {C80B8894-E742-4941-BB2E-BD4A414293A0}
		{F1488F85-1BF2-427A-B3C9-BD1F98C93A99} = {C80B8894-E742-4941-BB2E-BD4A414293A0}
		{184D92E1-FBE5-49D0-BDA8-9195DDF01840} = {4BA95578-6B9D-45F7-8856-5521CC36E904}
		{EE1E40BD-2C9C-4481-B522-A90F490E0C3F} = {184D92E1-FBE5-49D0-BDA8-9195DDF01840}
		{B1A10047-3B18-475B-BF26-B7CE41E28777} = {4BA95578-6B9D-45F7-8856-5521CC36E904}
		{F0A2210F-4D23-42A1-8FE9-0750CB7C11AB} = {B1A10047-3B18-475B-BF26-B7CE41E28777}
		{81B57710-00BB-42E8-B20E-CA308A90D42A} = {4BA95578-6B9D-45F7-8856-5521CC36E904}
		{EA889B6D-06F7-40F9-8362-9EA59D43689A} = {81B57710-00BB-42E8-B20E-CA308A90D42A}
		{88A3C8AB-F01C-419A-B148-B1D74F8DABB1} = {EA889B6D-06F7-40F9-8362-9EA59D43689A}
		{7240AFE9-E686-434B-B6C2-732F8840B357} = {EA889B6D-06F7-40F9-8362-9EA59D43689A}
		{AAC81072-3A68-46FE-B324-B797A1E1AB5A} = {EA889B6D-06F7-40F9-8362-9EA59D43689A}
		{B10A56BE-6A33-46D2-B07D-8323A8162F1B} = {EA889B6D-06F7-40F9-8362-9EA59D43689A}
	EndGlobalSection
EndGlobal
