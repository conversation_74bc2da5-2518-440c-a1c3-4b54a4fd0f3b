﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="11.9.0" />
    <PackageReference Include="MediatR" Version="12.2.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\BuildingBlocks\HarmoniHSE360.BuildingBlocks.Application\HarmoniHSE360.BuildingBlocks.Application.csproj" />
    <ProjectReference Include="..\HarmoniHSE360.Modules.UserManagement.Domain\HarmoniHSE360.Modules.UserManagement.Domain.csproj" />
  </ItemGroup>

</Project>
