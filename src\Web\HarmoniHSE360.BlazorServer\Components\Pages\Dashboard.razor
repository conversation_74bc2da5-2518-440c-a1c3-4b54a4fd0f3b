@page "/dashboard"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IJSRuntime JSRuntime

<PageTitle>Dashboard - HarmoniHSE360</PageTitle>

<div class="harmoni-container">
    <div class="dashboard-header">
        <h1 class="harmoni-display-title">HSE Dashboard</h1>
        <p class="harmoni-body-text harmoni-text-muted">
            Welcome back! Here's an overview of your HSE activities and metrics.
        </p>
    </div>
    
    <!-- Quick Stats Section -->
    <div class="stats-grid">
        <div class="stat-card harmoni-card">
            <div class="stat-icon incidents">📋</div>
            <div class="stat-content">
                <h3 class="stat-number">12</h3>
                <p class="stat-label">Active Incidents</p>
                <small class="stat-change positive">↗️ +2 this week</small>
            </div>
        </div>
        
        <div class="stat-card harmoni-card">
            <div class="stat-icon hazards">⚠️</div>
            <div class="stat-content">
                <h3 class="stat-number">8</h3>
                <p class="stat-label">Open Hazards</p>
                <small class="stat-change negative">↘️ -3 this week</small>
            </div>
        </div>
        
        <div class="stat-card harmoni-card">
            <div class="stat-icon compliance">✅</div>
            <div class="stat-content">
                <h3 class="stat-number">94%</h3>
                <p class="stat-label">Compliance Rate</p>
                <small class="stat-change positive">↗️ +1.2% this month</small>
            </div>
        </div>
        
        <div class="stat-card harmoni-card">
            <div class="stat-icon training">🎓</div>
            <div class="stat-content">
                <h3 class="stat-number">156</h3>
                <p class="stat-label">Training Complete</p>
                <small class="stat-change positive">↗️ +24 this week</small>
            </div>
        </div>
    </div>
    
    <!-- Main Content Grid -->
    <div class="dashboard-grid">
        <!-- Recent Activity -->
        <div class="dashboard-card harmoni-card">
            <div class="harmoni-card-header">
                <h2 class="harmoni-section-header">Recent Activity</h2>
                <button class="harmoni-btn harmoni-btn-secondary harmoni-btn-sm">View All</button>
            </div>
            <div class="harmoni-card-body">
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-icon incident">🚨</div>
                        <div class="activity-content">
                            <p class="activity-title">New incident reported in Building A</p>
                            <p class="activity-meta">Engineering Department • 2 hours ago</p>
                        </div>
                        <span class="activity-status high">High Priority</span>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon hazard">⚠️</div>
                        <div class="activity-content">
                            <p class="activity-title">Hazard assessment completed</p>
                            <p class="activity-meta">Facilities Team • 4 hours ago</p>
                        </div>
                        <span class="activity-status medium">Medium</span>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon training">📚</div>
                        <div class="activity-content">
                            <p class="activity-title">Fire safety training scheduled</p>
                            <p class="activity-meta">All Staff • 1 day ago</p>
                        </div>
                        <span class="activity-status low">Scheduled</span>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon compliance">✅</div>
                        <div class="activity-content">
                            <p class="activity-title">Monthly audit completed</p>
                            <p class="activity-meta">HSE Team • 2 days ago</p>
                        </div>
                        <span class="activity-status success">Complete</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="dashboard-card harmoni-card">
            <div class="harmoni-card-header">
                <h2 class="harmoni-section-header">Quick Actions</h2>
            </div>
            <div class="harmoni-card-body">
                <div class="quick-actions">
                    <AuthorizeView Roles="HSEManager,SystemAdministrator,Employee">
                        <Authorized>
                            <button class="quick-action-btn">
                                <div class="action-icon">📝</div>
                                <span>Report Incident</span>
                            </button>
                        </Authorized>
                    </AuthorizeView>
                    
                    <AuthorizeView Roles="HSEManager,SystemAdministrator,Employee">
                        <Authorized>
                            <button class="quick-action-btn">
                                <div class="action-icon">⚠️</div>
                                <span>Report Hazard</span>
                            </button>
                        </Authorized>
                    </AuthorizeView>
                    
                    <AuthorizeView Roles="HSEManager,SystemAdministrator">
                        <Authorized>
                            <button class="quick-action-btn">
                                <div class="action-icon">📊</div>
                                <span>View Analytics</span>
                            </button>
                        </Authorized>
                    </AuthorizeView>
                    
                    <AuthorizeView Roles="HSEManager,SystemAdministrator,DepartmentHead">
                        <Authorized>
                            <button class="quick-action-btn">
                                <div class="action-icon">📋</div>
                                <span>Audit Checklist</span>
                            </button>
                        </Authorized>
                    </AuthorizeView>
                    
                    <button class="quick-action-btn">
                        <div class="action-icon">🎓</div>
                        <span>My Training</span>
                    </button>
                    
                    <button class="quick-action-btn">
                        <div class="action-icon">📄</div>
                        <span>Documents</span>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Notifications -->
        <div class="dashboard-card harmoni-card">
            <div class="harmoni-card-header">
                <h2 class="harmoni-section-header">Notifications</h2>
                <button class="harmoni-btn harmoni-btn-secondary harmoni-btn-sm">Mark All Read</button>
            </div>
            <div class="harmoni-card-body">
                <div class="notifications-list">
                    <div class="notification-item unread">
                        <div class="notification-icon">🔔</div>
                        <div class="notification-content">
                            <p class="notification-title">Safety inspection due tomorrow</p>
                            <p class="notification-meta">Building C - Laboratory • Due: Dec 15</p>
                        </div>
                    </div>
                    
                    <div class="notification-item">
                        <div class="notification-icon">📧</div>
                        <div class="notification-content">
                            <p class="notification-title">Training reminder: First Aid Certification</p>
                            <p class="notification-meta">Scheduled for Dec 20, 2024</p>
                        </div>
                    </div>
                    
                    <div class="notification-item">
                        <div class="notification-icon">✅</div>
                        <div class="notification-content">
                            <p class="notification-title">Hazard assessment approved</p>
                            <p class="notification-meta">Playground area - Status updated</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Upcoming Events -->
        <div class="dashboard-card harmoni-card">
            <div class="harmoni-card-header">
                <h2 class="harmoni-section-header">Upcoming Events</h2>
                <button class="harmoni-btn harmoni-btn-secondary harmoni-btn-sm">Calendar</button>
            </div>
            <div class="harmoni-card-body">
                <div class="events-list">
                    <div class="event-item">
                        <div class="event-date">
                            <span class="event-day">15</span>
                            <span class="event-month">Dec</span>
                        </div>
                        <div class="event-content">
                            <p class="event-title">Fire Drill - All Buildings</p>
                            <p class="event-time">10:00 AM - 11:00 AM</p>
                        </div>
                    </div>
                    
                    <div class="event-item">
                        <div class="event-date">
                            <span class="event-day">18</span>
                            <span class="event-month">Dec</span>
                        </div>
                        <div class="event-content">
                            <p class="event-title">HSE Committee Meeting</p>
                            <p class="event-time">2:00 PM - 4:00 PM</p>
                        </div>
                    </div>
                    
                    <div class="event-item">
                        <div class="event-date">
                            <span class="event-day">20</span>
                            <span class="event-month">Dec</span>
                        </div>
                        <div class="event-content">
                            <p class="event-title">First Aid Training</p>
                            <p class="event-time">9:00 AM - 5:00 PM</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .dashboard-header {
        margin-bottom: var(--harmoni-spacing-xl);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--harmoni-spacing-lg);
        margin-bottom: var(--harmoni-spacing-xl);
    }
    
    .stat-card {
        display: flex;
        align-items: center;
        gap: var(--harmoni-spacing-md);
        padding: var(--harmoni-spacing-lg);
    }
    
    .stat-icon {
        font-size: 32px;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: var(--harmoni-radius-lg);
        background-color: rgba(0, 151, 167, 0.1);
    }
    
    .stat-content {
        flex: 1;
    }
    
    .stat-number {
        font-size: 24px;
        font-weight: 700;
        color: var(--harmoni-charcoal);
        margin: 0 0 4px 0;
    }
    
    .stat-label {
        font-size: 14px;
        color: var(--harmoni-charcoal);
        margin: 0 0 4px 0;
    }
    
    .stat-change {
        font-size: 12px;
        font-weight: 500;
    }
    
    .stat-change.positive {
        color: var(--harmoni-success);
    }
    
    .stat-change.negative {
        color: var(--harmoni-error);
    }
    
    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: var(--harmoni-spacing-lg);
    }
    
    .dashboard-card {
        height: fit-content;
    }
    
    .activity-list {
        display: flex;
        flex-direction: column;
        gap: var(--harmoni-spacing-md);
    }
    
    .activity-item {
        display: flex;
        align-items: center;
        gap: var(--harmoni-spacing-md);
        padding: var(--harmoni-spacing-md);
        border-radius: var(--harmoni-radius-md);
        background-color: var(--harmoni-soft-grey);
    }
    
    .activity-icon {
        font-size: 20px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: var(--harmoni-radius-md);
        background-color: var(--harmoni-white);
    }
    
    .activity-content {
        flex: 1;
    }
    
    .activity-title {
        font-weight: 500;
        margin: 0 0 4px 0;
    }
    
    .activity-meta {
        font-size: 12px;
        color: #666;
        margin: 0;
    }
    
    .activity-status {
        font-size: 11px;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: var(--harmoni-radius-sm);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .activity-status.high {
        background-color: #ffebee;
        color: #c62828;
    }
    
    .activity-status.medium {
        background-color: #fff3e0;
        color: #e65100;
    }
    
    .activity-status.low {
        background-color: #e3f2fd;
        color: #1565c0;
    }
    
    .activity-status.success {
        background-color: #e8f5e8;
        color: #2e7d32;
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: var(--harmoni-spacing-md);
    }
    
    .quick-action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--harmoni-spacing-sm);
        padding: var(--harmoni-spacing-lg);
        border: 2px solid var(--harmoni-border-grey);
        border-radius: var(--harmoni-radius-md);
        background-color: var(--harmoni-white);
        color: var(--harmoni-charcoal);
        text-decoration: none;
        cursor: pointer;
        transition: var(--harmoni-transition);
        font-family: var(--harmoni-font-primary);
        font-size: 14px;
        font-weight: 500;
    }
    
    .quick-action-btn:hover {
        border-color: var(--harmoni-teal-primary);
        background-color: rgba(0, 151, 167, 0.05);
        color: var(--harmoni-teal-primary);
    }
    
    .action-icon {
        font-size: 24px;
    }
    
    .notifications-list {
        display: flex;
        flex-direction: column;
        gap: var(--harmoni-spacing-md);
    }
    
    .notification-item {
        display: flex;
        align-items: flex-start;
        gap: var(--harmoni-spacing-md);
        padding: var(--harmoni-spacing-md);
        border-radius: var(--harmoni-radius-md);
        background-color: var(--harmoni-soft-grey);
    }
    
    .notification-item.unread {
        background-color: rgba(0, 151, 167, 0.05);
        border-left: 3px solid var(--harmoni-teal-primary);
    }
    
    .notification-icon {
        font-size: 16px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: var(--harmoni-radius-md);
        background-color: var(--harmoni-white);
        flex-shrink: 0;
    }
    
    .notification-content {
        flex: 1;
    }
    
    .notification-title {
        font-weight: 500;
        margin: 0 0 4px 0;
        font-size: 14px;
    }
    
    .notification-meta {
        font-size: 12px;
        color: #666;
        margin: 0;
    }
    
    .events-list {
        display: flex;
        flex-direction: column;
        gap: var(--harmoni-spacing-md);
    }
    
    .event-item {
        display: flex;
        align-items: center;
        gap: var(--harmoni-spacing-md);
        padding: var(--harmoni-spacing-md);
        border-radius: var(--harmoni-radius-md);
        background-color: var(--harmoni-soft-grey);
    }
    
    .event-date {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 50px;
        padding: var(--harmoni-spacing-sm);
        border-radius: var(--harmoni-radius-md);
        background-color: var(--harmoni-teal-primary);
        color: var(--harmoni-white);
        text-align: center;
        flex-shrink: 0;
    }
    
    .event-day {
        font-size: 18px;
        font-weight: 700;
        line-height: 1;
    }
    
    .event-month {
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        line-height: 1;
    }
    
    .event-content {
        flex: 1;
    }
    
    .event-title {
        font-weight: 500;
        margin: 0 0 4px 0;
        font-size: 14px;
    }
    
    .event-time {
        font-size: 12px;
        color: #666;
        margin: 0;
    }
    
    /* Mobile Responsive */
    @@media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
        
        .dashboard-grid {
            grid-template-columns: 1fr;
        }
        
        .quick-actions {
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        }
        
        .stat-card {
            flex-direction: column;
            text-align: center;
        }
    }
</style>

@code {
    protected override async Task OnInitializedAsync()
    {
        // Initialize dashboard data
        await Task.CompletedTask;
    }
}