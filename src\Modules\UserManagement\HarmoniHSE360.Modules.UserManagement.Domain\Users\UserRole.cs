using HarmoniHSE360.BuildingBlocks.Domain;

namespace HarmoniHSE360.Modules.UserManagement.Domain.Users;

public sealed class UserRole : Entity<Guid>
{
    public Guid UserId { get; private set; }
    public Guid RoleId { get; private set; }
    public DateTime AssignedAt { get; private set; }
    public string? AssignedBy { get; private set; }

    public User User { get; private set; } = null!;
    public Role Role { get; private set; } = null!;

    private UserRole() { }

    private UserRole(Guid userId, Guid roleId, string? assignedBy)
    {
        Id = Guid.NewGuid();
        UserId = userId;
        RoleId = roleId;
        AssignedBy = assignedBy;
        AssignedAt = DateTime.UtcNow;
    }

    public static UserRole Create(Guid userId, Guid roleId, string? assignedBy = null)
    {
        return new UserRole(userId, roleId, assignedBy);
    }
}