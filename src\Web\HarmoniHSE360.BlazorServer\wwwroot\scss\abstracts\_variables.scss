// HarmoniHSE360 Design System Variables
// Based on the brand color palette and design tokens

// ============================================
// Brand Colors
// ============================================
// Primary Colors
$harmoni-teal-primary: #0097A7;
$harmoni-deep-blue: #002B5C;

// Secondary Colors
$harmoni-leaf-green: #8BC34A;
$harmoni-sunset-orange: #FF6F00;

// Neutral Colors
$harmoni-cool-grey: #B0BEC5;
$harmoni-charcoal: #37474F;
$harmoni-soft-white: #FAFAFA;
$harmoni-border-grey: #E0E0E0;

// Status Colors
$harmoni-error: #F44336;
$harmoni-warning: #FF9800;
$harmoni-success: #4CAF50;
$harmoni-info: #2196F3;

// ============================================
// Typography
// ============================================
// Font Families
$harmoni-font-primary: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
$harmoni-font-secondary: 'Poppins', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
$harmoni-font-mono: 'Roboto Mono', 'Courier New', monospace;

// Font Sizes
$harmoni-font-size-xs: 0.75rem;    // 12px
$harmoni-font-size-sm: 0.875rem;   // 14px
$harmoni-font-size-base: 1rem;     // 16px
$harmoni-font-size-lg: 1.125rem;   // 18px
$harmoni-font-size-xl: 1.25rem;    // 20px
$harmoni-font-size-2xl: 1.5rem;    // 24px
$harmoni-font-size-3xl: 1.875rem;  // 30px
$harmoni-font-size-4xl: 2.25rem;   // 36px

// Font Weights
$harmoni-font-weight-light: 300;
$harmoni-font-weight-normal: 400;
$harmoni-font-weight-medium: 500;
$harmoni-font-weight-semibold: 600;
$harmoni-font-weight-bold: 700;

// Line Heights
$harmoni-line-height-tight: 1.25;
$harmoni-line-height-normal: 1.5;
$harmoni-line-height-relaxed: 1.75;

// ============================================
// Spacing
// ============================================
$harmoni-spacing-xs: 0.25rem;   // 4px
$harmoni-spacing-sm: 0.5rem;    // 8px
$harmoni-spacing-md: 1rem;      // 16px
$harmoni-spacing-lg: 1.5rem;    // 24px
$harmoni-spacing-xl: 2rem;      // 32px
$harmoni-spacing-2xl: 3rem;     // 48px
$harmoni-spacing-3xl: 4rem;     // 64px

// ============================================
// Border Radius
// ============================================
$harmoni-radius-sm: 0.25rem;    // 4px
$harmoni-radius-md: 0.5rem;     // 8px
$harmoni-radius-lg: 0.75rem;    // 12px
$harmoni-radius-xl: 1rem;       // 16px
$harmoni-radius-full: 9999px;

// ============================================
// Shadows
// ============================================
$harmoni-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$harmoni-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$harmoni-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$harmoni-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

// ============================================
// Transitions
// ============================================
$harmoni-transition-fast: 150ms ease-in-out;
$harmoni-transition: 300ms ease-in-out;
$harmoni-transition-slow: 500ms ease-in-out;

// ============================================
// Z-Index
// ============================================
$harmoni-z-dropdown: 1000;
$harmoni-z-sticky: 1020;
$harmoni-z-fixed: 1030;
$harmoni-z-modal-backdrop: 1040;
$harmoni-z-modal: 1050;
$harmoni-z-popover: 1060;
$harmoni-z-tooltip: 1070;

// ============================================
// Breakpoints
// ============================================
$harmoni-breakpoint-xs: 480px;
$harmoni-breakpoint-sm: 576px;
$harmoni-breakpoint-md: 768px;
$harmoni-breakpoint-lg: 992px;
$harmoni-breakpoint-xl: 1200px;
$harmoni-breakpoint-2xl: 1400px;

// ============================================
// Container Widths
// ============================================
$harmoni-container-sm: 540px;
$harmoni-container-md: 720px;
$harmoni-container-lg: 960px;
$harmoni-container-xl: 1140px;
$harmoni-container-2xl: 1320px;