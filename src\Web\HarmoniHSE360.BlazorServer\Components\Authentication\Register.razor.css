.register-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--harmoni-teal-primary) 0%, var(--harmoni-deep-blue) 100%);
    padding: 24px;
}

.register-wrapper {
    width: 100%;
    max-width: 1000px;
}

.register-card {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border-radius: var(--harmoni-radius-lg);
    border: none;
    overflow: hidden;
}

.brand-header {
    text-align: center;
    padding: 32px 0 24px;
    border-bottom: 1px solid var(--harmoni-border-grey);
    margin-bottom: 32px;
    background: linear-gradient(135deg, #f8fffe 0%, #e0f7fa 100%);
}

.brand-title {
    color: var(--harmoni-teal-primary) !important;
    font-weight: 700 !important;
    margin-bottom: 8px !important;
    font-size: 28px !important;
    font-family: var(--harmoni-font-secondary) !important;
}

.brand-subtitle {
    font-size: 16px;
    margin: 0;
    color: var(--harmoni-charcoal);
}

.register-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 48px;
    padding: 0 24px 24px;
}

.register-form {
    max-width: 600px;
}

.password-requirements {
    margin-top: 4px;
}

.requirement-text {
    font-size: 12px;
    line-height: 1.4;
}

.info-section {
    position: sticky;
    top: 24px;
    height: fit-content;
}

.info-card {
    background: linear-gradient(135deg, #f8fffe 0%, #e0f7fa 100%);
    border: 1px solid rgba(0, 151, 167, 0.2);
    border-radius: var(--harmoni-radius-md);
}

.info-title {
    color: var(--harmoni-teal-primary) !important;
    margin-bottom: 16px !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.info-icon {
    color: var(--harmoni-teal-primary);
    font-size: 18px;
}

.features-list {
    margin: 24px 0;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.feature-icon {
    color: var(--harmoni-leaf-green);
    font-size: 16px;
    flex-shrink: 0;
}

.contact-info {
    margin-top: 8px;
}

.contact-text {
    font-size: 13px;
    margin: 0;
    line-height: 1.4;
}

.login-link {
    text-align: center;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid var(--harmoni-border-grey);
}

.login-link-text {
    color: var(--harmoni-teal-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--harmoni-transition);
}

.login-link-text:hover {
    color: var(--harmoni-deep-blue);
    text-decoration: underline;
}

/* Ant Design component customizations */
::deep .ant-btn-primary {
    background-color: var(--harmoni-teal-primary);
    border-color: var(--harmoni-teal-primary);
}

::deep .ant-btn-primary:hover {
    background-color: var(--harmoni-deep-blue);
    border-color: var(--harmoni-deep-blue);
}

::deep .ant-input:focus,
::deep .ant-input-focused,
::deep .ant-select-focused .ant-select-selector {
    border-color: var(--harmoni-teal-primary);
    box-shadow: 0 0 0 2px rgba(0, 151, 167, 0.1);
}

::deep .ant-checkbox-checked .ant-checkbox-inner {
    background-color: var(--harmoni-teal-primary);
    border-color: var(--harmoni-teal-primary);
}

::deep .ant-alert-error {
    border: 1px solid var(--harmoni-error);
    background-color: rgba(244, 67, 54, 0.05);
}

::deep .ant-alert-success {
    border: 1px solid var(--harmoni-success);
    background-color: rgba(76, 175, 80, 0.05);
}

::deep .ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background-color: rgba(0, 151, 167, 0.1);
    color: var(--harmoni-teal-primary);
}

/* Mobile responsive */
@media (max-width: 768px) {
    .register-content {
        grid-template-columns: 1fr;
        gap: 24px;
    }
    
    .info-section {
        order: -1;
        position: static;
    }
    
    .brand-header {
        padding: 24px 0 16px;
    }
    
    .brand-title {
        font-size: 24px !important;
    }
    
    .register-content {
        padding: 0 16px 16px;
    }
}

@media (max-width: 480px) {
    .register-container {
        padding: 16px;
    }
    
    .register-content {
        padding: 0 12px 12px;
    }
}