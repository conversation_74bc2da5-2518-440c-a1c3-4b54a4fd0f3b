using HarmoniHSE360.Modules.UserManagement.Domain.Users;

namespace HarmoniHSE360.Modules.UserManagement.Api;

public sealed record RegisterUserRequest
{
    public string Email { get; init; } = string.Empty;
    public string Password { get; init; } = string.Empty;
    public string FirstName { get; init; } = string.Empty;
    public string LastName { get; init; } = string.Empty;
    public string? PhoneNumber { get; init; }
    public AuthenticationSource AuthenticationSource { get; init; } = AuthenticationSource.Local;
    public string? ExternalId { get; init; }
    public string? Department { get; init; }
    public string? Position { get; init; }
}