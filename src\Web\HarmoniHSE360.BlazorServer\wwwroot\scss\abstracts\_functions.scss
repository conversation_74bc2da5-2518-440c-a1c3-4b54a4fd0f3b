// HarmoniHSE360 SCSS Functions
// Utility functions for calculations and transformations

// ============================================
// Color Functions
// ============================================

// Convert pixel values to rem
@function rem($pixels, $base: 16) {
  @return ($pixels / $base) * 1rem;
}

// Convert pixel values to em
@function em($pixels, $base: 16) {
  @return ($pixels / $base) * 1em;
}

// Get color with opacity
@function rgba-color($color, $opacity) {
  @return rgba($color, $opacity);
}

// Tint color (mix with white)
@function tint($color, $percentage) {
  @return mix(white, $color, $percentage);
}

// Shade color (mix with black)
@function shade($color, $percentage) {
  @return mix(black, $color, $percentage);
}

// ============================================
// Spacing Functions
// ============================================

// Get spacing value by name
@function space($size) {
  @if $size == xs {
    @return $harmoni-spacing-xs;
  } @else if $size == sm {
    @return $harmoni-spacing-sm;
  } @else if $size == md {
    @return $harmoni-spacing-md;
  } @else if $size == lg {
    @return $harmoni-spacing-lg;
  } @else if $size == xl {
    @return $harmoni-spacing-xl;
  } @else if $size == 2xl {
    @return $harmoni-spacing-2xl;
  } @else if $size == 3xl {
    @return $harmoni-spacing-3xl;
  } @else {
    @return $size;
  }
}

// ============================================
// Typography Functions
// ============================================

// Get font size by name
@function font-size($size) {
  @if $size == xs {
    @return $harmoni-font-size-xs;
  } @else if $size == sm {
    @return $harmoni-font-size-sm;
  } @else if $size == base {
    @return $harmoni-font-size-base;
  } @else if $size == lg {
    @return $harmoni-font-size-lg;
  } @else if $size == xl {
    @return $harmoni-font-size-xl;
  } @else if $size == 2xl {
    @return $harmoni-font-size-2xl;
  } @else if $size == 3xl {
    @return $harmoni-font-size-3xl;
  } @else if $size == 4xl {
    @return $harmoni-font-size-4xl;
  } @else {
    @return $size;
  }
}

// Calculate line height based on font size
@function line-height($font-size, $line-height-multiplier: 1.5) {
  @return $font-size * $line-height-multiplier;
}

// ============================================
// Z-Index Functions
// ============================================

// Get z-index value by layer name
@function z($layer) {
  @if $layer == dropdown {
    @return $harmoni-z-dropdown;
  } @else if $layer == sticky {
    @return $harmoni-z-sticky;
  } @else if $layer == fixed {
    @return $harmoni-z-fixed;
  } @else if $layer == modal-backdrop {
    @return $harmoni-z-modal-backdrop;
  } @else if $layer == modal {
    @return $harmoni-z-modal;
  } @else if $layer == popover {
    @return $harmoni-z-popover;
  } @else if $layer == tooltip {
    @return $harmoni-z-tooltip;
  } @else {
    @return $layer;
  }
}

// ============================================
// Math Functions
// ============================================

// Strip unit from value
@function strip-unit($value) {
  @return $value / ($value * 0 + 1);
}

// Calculate percentage
@function percentage($partial, $total) {
  @return ($partial / $total) * 100%;
}

// Clamp value between min and max
@function clamp-value($min, $val, $max) {
  @return max($min, min($val, $max));
}