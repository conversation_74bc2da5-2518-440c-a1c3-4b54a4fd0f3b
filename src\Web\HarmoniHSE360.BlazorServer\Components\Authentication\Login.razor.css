.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--harmoni-teal-primary) 0%, var(--harmoni-deep-blue) 100%);
    padding: 24px;
}

.login-wrapper {
    width: 100%;
    max-width: 400px;
}

.login-card {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border-radius: var(--harmoni-radius-lg);
    border: none;
    overflow: hidden;
}

.brand-header {
    text-align: center;
    padding: 32px 0 24px;
    border-bottom: 1px solid var(--harmoni-border-grey);
    margin-bottom: 32px;
    background: linear-gradient(135deg, #f8fffe 0%, #e0f7fa 100%);
}

.brand-title {
    color: var(--harmoni-teal-primary) !important;
    font-weight: 700 !important;
    margin-bottom: 8px !important;
    font-size: 28px !important;
    font-family: var(--harmoni-font-secondary) !important;
}

.brand-subtitle {
    font-size: 14px;
    margin: 0;
    color: var(--harmoni-charcoal);
}

.login-form {
    padding: 0 24px 24px;
}

.additional-actions {
    margin-top: 24px;
    text-align: center;
}

.forgot-password-link {
    color: var(--harmoni-teal-primary);
    text-decoration: none;
    font-size: 14px;
    transition: var(--harmoni-transition);
}

.forgot-password-link:hover {
    color: var(--harmoni-deep-blue);
    text-decoration: underline;
}

.register-link {
    text-align: center;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid var(--harmoni-border-grey);
}

.register-link-text {
    color: var(--harmoni-teal-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--harmoni-transition);
}

.register-link-text:hover {
    color: var(--harmoni-deep-blue);
    text-decoration: underline;
}

/* Ant Design component customizations */
::deep .ant-btn-primary {
    background-color: var(--harmoni-teal-primary);
    border-color: var(--harmoni-teal-primary);
}

::deep .ant-btn-primary:hover {
    background-color: var(--harmoni-deep-blue);
    border-color: var(--harmoni-deep-blue);
}

::deep .ant-input:focus,
::deep .ant-input-focused {
    border-color: var(--harmoni-teal-primary);
    box-shadow: 0 0 0 2px rgba(0, 151, 167, 0.1);
}

::deep .ant-checkbox-checked .ant-checkbox-inner {
    background-color: var(--harmoni-teal-primary);
    border-color: var(--harmoni-teal-primary);
}

::deep .ant-alert-error {
    border: 1px solid var(--harmoni-error);
    background-color: rgba(244, 67, 54, 0.05);
}

::deep .ant-alert-success {
    border: 1px solid var(--harmoni-success);
    background-color: rgba(76, 175, 80, 0.05);
}

/* Mobile responsive */
@media (max-width: 480px) {
    .login-container {
        padding: 16px;
    }
    
    .brand-header {
        padding: 24px 0 16px;
    }
    
    .brand-title {
        font-size: 24px !important;
    }
    
    .login-form {
        padding: 0 16px 16px;
    }
}