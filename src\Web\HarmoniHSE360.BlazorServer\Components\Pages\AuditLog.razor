@page "/admin/audit-log"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize(Roles = "SystemAdministrator,HSEManager")]
@inject IJSRuntime JSRuntime

<PageTitle>Audit Log - HarmoniHSE360</PageTitle>

<div class="harmoni-container">
    <div class="audit-log-header">
        <h1 class="harmoni-display-title">Audit Log</h1>
        <p class="harmoni-body-text harmoni-text-muted">
            Track all system activities and user actions for compliance and security monitoring.
        </p>
    </div>
    
    <!-- Filters Section -->
    <div class="harmoni-card filters-card">
        <div class="harmoni-card-header">
            <h2 class="harmoni-section-header">Filters</h2>
            <button class="harmoni-btn harmoni-btn-secondary harmoni-btn-sm" @onclick="ClearFilters">
                Clear All
            </button>
        </div>
        <div class="harmoni-card-body">
            <div class="filters-grid">
                <div class="filter-group">
                    <label class="harmoni-form-label">Date Range</label>
                    <div class="date-range">
                        <input type="date" @bind="startDate" class="harmoni-form-input" />
                        <span class="date-separator">to</span>
                        <input type="date" @bind="endDate" class="harmoni-form-input" />
                    </div>
                </div>
                
                <div class="filter-group">
                    <label class="harmoni-form-label">Event Type</label>
                    <select @bind="selectedEventType" class="harmoni-form-input">
                        <option value="">All Events</option>
                        <option value="Authentication">Authentication</option>
                        <option value="Authorization">Authorization</option>
                        <option value="DataAccess">Data Access</option>
                        <option value="DataModification">Data Modification</option>
                        <option value="SystemConfiguration">System Configuration</option>
                        <option value="UserManagement">User Management</option>
                        <option value="IncidentManagement">Incident Management</option>
                        <option value="HazardReporting">Hazard Reporting</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="harmoni-form-label">Severity Level</label>
                    <select @bind="selectedSeverity" class="harmoni-form-input">
                        <option value="">All Levels</option>
                        <option value="Low">Low</option>
                        <option value="Medium">Medium</option>
                        <option value="High">High</option>
                        <option value="Critical">Critical</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="harmoni-form-label">User</label>
                    <input type="text" @bind="userFilter" placeholder="Search by user email or name" class="harmoni-form-input" />
                </div>
                
                <div class="filter-group">
                    <label class="harmoni-form-label">IP Address</label>
                    <input type="text" @bind="ipFilter" placeholder="Search by IP address" class="harmoni-form-input" />
                </div>
                
                <div class="filter-group">
                    <label class="harmoni-form-label">Resource</label>
                    <input type="text" @bind="resourceFilter" placeholder="Search by resource or module" class="harmoni-form-input" />
                </div>
            </div>
            
            <div class="filter-actions">
                <button class="harmoni-btn harmoni-btn-primary" @onclick="ApplyFilters" disabled="@isLoading">
                    @if (isLoading)
                    {
                        <span class="harmoni-spinner"></span>
                        <span>Applying...</span>
                    }
                    else
                    {
                        <span>Apply Filters</span>
                    }
                </button>
                
                <button class="harmoni-btn harmoni-btn-secondary" @onclick="ExportAuditLog">
                    📥 Export
                </button>
                
                <button class="harmoni-btn harmoni-btn-secondary" @onclick="RefreshData">
                    🔄 Refresh
                </button>
            </div>
        </div>
    </div>
    
    <!-- Audit Log Table -->
    <div class="harmoni-card audit-table-card">
        <div class="harmoni-card-header">
            <h2 class="harmoni-section-header">Audit Events</h2>
            <div class="table-info">
                <span class="harmoni-caption harmoni-text-muted">
                    Showing @filteredEntries.Count of @auditEntries.Count entries
                </span>
            </div>
        </div>
        <div class="harmoni-card-body">
            @if (isLoading)
            {
                <div class="loading-state harmoni-text-center">
                    <div class="harmoni-spinner" style="font-size: 24px; margin-bottom: var(--harmoni-spacing-md);"></div>
                    <p class="harmoni-body-text harmoni-text-muted">Loading audit log entries...</p>
                </div>
            }
            else if (!filteredEntries.Any())
            {
                <div class="empty-state harmoni-text-center">
                    <div class="empty-icon">📝</div>
                    <h3 class="harmoni-section-header">No audit entries found</h3>
                    <p class="harmoni-body-text harmoni-text-muted">
                        Try adjusting your filters or check back later for new audit entries.
                    </p>
                </div>
            }
            else
            {
                <div class="audit-table-container">
                    <table class="audit-table">
                        <thead>
                            <tr>
                                <th @onclick="() => SortBy(nameof(AuditEntry.Timestamp))">
                                    Timestamp 
                                    <span class="sort-indicator">@GetSortIndicator(nameof(AuditEntry.Timestamp))</span>
                                </th>
                                <th @onclick="() => SortBy(nameof(AuditEntry.EventType))">
                                    Event Type
                                    <span class="sort-indicator">@GetSortIndicator(nameof(AuditEntry.EventType))</span>
                                </th>
                                <th @onclick="() => SortBy(nameof(AuditEntry.Severity))">
                                    Severity
                                    <span class="sort-indicator">@GetSortIndicator(nameof(AuditEntry.Severity))</span>
                                </th>
                                <th @onclick="() => SortBy(nameof(AuditEntry.User))">
                                    User
                                    <span class="sort-indicator">@GetSortIndicator(nameof(AuditEntry.User))</span>
                                </th>
                                <th @onclick="() => SortBy(nameof(AuditEntry.Action))">
                                    Action
                                    <span class="sort-indicator">@GetSortIndicator(nameof(AuditEntry.Action))</span>
                                </th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var entry in GetPagedEntries())
                            {
                                <tr class="audit-row @GetRowClass(entry.Severity)" @onclick="() => ShowDetails(entry)">
                                    <td class="timestamp-cell">
                                        <div class="timestamp-content">
                                            <span class="date">@entry.Timestamp.ToString("MMM dd, yyyy")</span>
                                            <span class="time">@entry.Timestamp.ToString("HH:mm:ss")</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="event-type @entry.EventType.ToLower()">
                                            @GetEventIcon(entry.EventType) @entry.EventType
                                        </span>
                                    </td>
                                    <td>
                                        <span class="severity-badge @entry.Severity.ToLower()">
                                            @entry.Severity
                                        </span>
                                    </td>
                                    <td class="user-cell">
                                        <div class="user-info">
                                            <span class="user-name">@entry.User</span>
                                            <span class="user-ip">@entry.IpAddress</span>
                                        </div>
                                    </td>
                                    <td class="action-cell">@entry.Action</td>
                                    <td class="details-cell">
                                        <span class="details-preview">@TruncateText(entry.Details, 50)</span>
                                        <button class="details-btn harmoni-btn-sm">View</button>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        <span class="harmoni-caption harmoni-text-muted">
                            Page @currentPage of @totalPages
                        </span>
                    </div>
                    <div class="pagination-controls">
                        <button class="harmoni-btn harmoni-btn-secondary harmoni-btn-sm" 
                                @onclick="PreviousPage" 
                                disabled="@(currentPage <= 1)">
                            Previous
                        </button>
                        <button class="harmoni-btn harmoni-btn-secondary harmoni-btn-sm" 
                                @onclick="NextPage" 
                                disabled="@(currentPage >= totalPages)">
                            Next
                        </button>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Audit Entry Details Modal -->
@if (selectedEntry != null && showDetailsModal)
{
    <div class="modal-overlay" @onclick="CloseDetailsModal">
        <div class="modal-content harmoni-card" @onclick:stopPropagation="true">
            <div class="harmoni-card-header">
                <h2 class="harmoni-section-header">Audit Entry Details</h2>
                <button class="close-btn" @onclick="CloseDetailsModal">✕</button>
            </div>
            <div class="harmoni-card-body">
                <div class="detail-row">
                    <label>Timestamp:</label>
                    <span>@selectedEntry.Timestamp.ToString("yyyy-MM-dd HH:mm:ss UTC")</span>
                </div>
                <div class="detail-row">
                    <label>Event ID:</label>
                    <span class="monospace">@selectedEntry.Id</span>
                </div>
                <div class="detail-row">
                    <label>Event Type:</label>
                    <span class="event-type @selectedEntry.EventType.ToLower()">
                        @GetEventIcon(selectedEntry.EventType) @selectedEntry.EventType
                    </span>
                </div>
                <div class="detail-row">
                    <label>Severity:</label>
                    <span class="severity-badge @selectedEntry.Severity.ToLower()">@selectedEntry.Severity</span>
                </div>
                <div class="detail-row">
                    <label>User:</label>
                    <span>@selectedEntry.User</span>
                </div>
                <div class="detail-row">
                    <label>IP Address:</label>
                    <span class="monospace">@selectedEntry.IpAddress</span>
                </div>
                <div class="detail-row">
                    <label>User Agent:</label>
                    <span class="monospace">@selectedEntry.UserAgent</span>
                </div>
                <div class="detail-row">
                    <label>Resource:</label>
                    <span>@selectedEntry.Resource</span>
                </div>
                <div class="detail-row">
                    <label>Action:</label>
                    <span>@selectedEntry.Action</span>
                </div>
                <div class="detail-row detail-full">
                    <label>Details:</label>
                    <div class="details-content">@selectedEntry.Details</div>
                </div>
                @if (!string.IsNullOrEmpty(selectedEntry.AdditionalData))
                {
                    <div class="detail-row detail-full">
                        <label>Additional Data:</label>
                        <pre class="additional-data">@selectedEntry.AdditionalData</pre>
                    </div>
                }
            </div>
        </div>
    </div>
}

<style>
    .audit-log-header {
        margin-bottom: var(--harmoni-spacing-xl);
    }
    
    .filters-card {
        margin-bottom: var(--harmoni-spacing-lg);
    }
    
    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--harmoni-spacing-md);
        margin-bottom: var(--harmoni-spacing-lg);
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
        gap: var(--harmoni-spacing-xs);
    }
    
    .date-range {
        display: flex;
        align-items: center;
        gap: var(--harmoni-spacing-sm);
    }
    
    .date-separator {
        font-size: 14px;
        color: #666;
        white-space: nowrap;
    }
    
    .filter-actions {
        display: flex;
        gap: var(--harmoni-spacing-md);
        flex-wrap: wrap;
    }
    
    .audit-table-card {
        overflow: hidden;
    }
    
    .table-info {
        display: flex;
        align-items: center;
        gap: var(--harmoni-spacing-md);
    }
    
    .loading-state {
        padding: var(--harmoni-spacing-xxl);
    }
    
    .empty-state {
        padding: var(--harmoni-spacing-xxl);
    }
    
    .empty-icon {
        font-size: 48px;
        margin-bottom: var(--harmoni-spacing-md);
    }
    
    .audit-table-container {
        overflow-x: auto;
    }
    
    .audit-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
    }
    
    .audit-table th {
        background-color: var(--harmoni-soft-grey);
        color: var(--harmoni-charcoal);
        font-weight: 600;
        padding: var(--harmoni-spacing-md);
        text-align: left;
        border-bottom: 1px solid var(--harmoni-border-grey);
        cursor: pointer;
        user-select: none;
        white-space: nowrap;
    }
    
    .audit-table th:hover {
        background-color: rgba(0, 151, 167, 0.1);
    }
    
    .sort-indicator {
        font-size: 12px;
        margin-left: 4px;
        color: #666;
    }
    
    .audit-table td {
        padding: var(--harmoni-spacing-md);
        border-bottom: 1px solid var(--harmoni-border-grey);
        vertical-align: top;
    }
    
    .audit-row {
        cursor: pointer;
        transition: var(--harmoni-transition);
    }
    
    .audit-row:hover {
        background-color: rgba(0, 151, 167, 0.05);
    }
    
    .audit-row.high {
        border-left: 3px solid var(--harmoni-error);
    }
    
    .audit-row.critical {
        border-left: 3px solid #d32f2f;
        background-color: rgba(244, 67, 54, 0.05);
    }
    
    .timestamp-content {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }
    
    .timestamp-content .date {
        font-weight: 500;
    }
    
    .timestamp-content .time {
        font-size: 12px;
        color: #666;
        font-family: 'Courier New', monospace;
    }
    
    .event-type {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border-radius: var(--harmoni-radius-sm);
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .event-type.authentication {
        background-color: #e3f2fd;
        color: #1565c0;
    }
    
    .event-type.authorization {
        background-color: #fff3e0;
        color: #e65100;
    }
    
    .event-type.dataaccess {
        background-color: #f3e5f5;
        color: #7b1fa2;
    }
    
    .event-type.datamodification {
        background-color: #ffebee;
        color: #c62828;
    }
    
    .event-type.systemconfiguration {
        background-color: #e8f5e8;
        color: #2e7d32;
    }
    
    .event-type.usermanagement {
        background-color: #e1f5fe;
        color: #0277bd;
    }
    
    .severity-badge {
        padding: 4px 8px;
        border-radius: var(--harmoni-radius-sm);
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .severity-badge.low {
        background-color: #e8f5e8;
        color: #2e7d32;
    }
    
    .severity-badge.medium {
        background-color: #fff3e0;
        color: #e65100;
    }
    
    .severity-badge.high {
        background-color: #ffebee;
        color: #c62828;
    }
    
    .severity-badge.critical {
        background-color: #ffebee;
        color: #b71c1c;
        border: 1px solid #ffcdd2;
    }
    
    .user-info {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }
    
    .user-name {
        font-weight: 500;
    }
    
    .user-ip {
        font-size: 12px;
        color: #666;
        font-family: 'Courier New', monospace;
    }
    
    .details-cell {
        position: relative;
        max-width: 200px;
    }
    
    .details-preview {
        display: block;
        margin-bottom: 4px;
    }
    
    .details-btn {
        font-size: 11px;
        padding: 2px 6px;
        background-color: var(--harmoni-teal-primary);
        color: var(--harmoni-white);
        border: none;
        border-radius: var(--harmoni-radius-sm);
        cursor: pointer;
    }
    
    .pagination-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: var(--harmoni-spacing-lg);
        padding-top: var(--harmoni-spacing-md);
        border-top: 1px solid var(--harmoni-border-grey);
    }
    
    .pagination-controls {
        display: flex;
        gap: var(--harmoni-spacing-sm);
    }
    
    /* Modal Styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        padding: var(--harmoni-spacing-md);
    }
    
    .modal-content {
        max-width: 600px;
        width: 100%;
        max-height: 80vh;
        overflow-y: auto;
        position: relative;
    }
    
    .close-btn {
        position: absolute;
        top: var(--harmoni-spacing-md);
        right: var(--harmoni-spacing-md);
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #666;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: var(--harmoni-transition);
    }
    
    .close-btn:hover {
        background-color: var(--harmoni-soft-grey);
        color: var(--harmoni-charcoal);
    }
    
    .detail-row {
        display: grid;
        grid-template-columns: 140px 1fr;
        gap: var(--harmoni-spacing-md);
        margin-bottom: var(--harmoni-spacing-md);
        padding-bottom: var(--harmoni-spacing-sm);
        border-bottom: 1px solid var(--harmoni-border-grey);
    }
    
    .detail-row.detail-full {
        grid-template-columns: 1fr;
        gap: var(--harmoni-spacing-sm);
    }
    
    .detail-row label {
        font-weight: 600;
        color: var(--harmoni-charcoal);
        font-size: 14px;
    }
    
    .monospace {
        font-family: 'Courier New', monospace;
        font-size: 13px;
        background-color: var(--harmoni-soft-grey);
        padding: 2px 4px;
        border-radius: var(--harmoni-radius-sm);
    }
    
    .details-content {
        background-color: var(--harmoni-soft-grey);
        padding: var(--harmoni-spacing-md);
        border-radius: var(--harmoni-radius-md);
        font-size: 14px;
        line-height: 1.5;
    }
    
    .additional-data {
        background-color: var(--harmoni-soft-grey);
        padding: var(--harmoni-spacing-md);
        border-radius: var(--harmoni-radius-md);
        font-size: 12px;
        overflow-x: auto;
        white-space: pre-wrap;
        margin: 0;
    }
    
    /* Mobile Responsive */
    @@media (max-width: 768px) {
        .filters-grid {
            grid-template-columns: 1fr;
        }
        
        .filter-actions {
            justify-content: stretch;
        }
        
        .filter-actions .harmoni-btn {
            flex: 1;
        }
        
        .audit-table {
            font-size: 12px;
        }
        
        .audit-table th,
        .audit-table td {
            padding: var(--harmoni-spacing-sm);
        }
        
        .pagination-container {
            flex-direction: column;
            gap: var(--harmoni-spacing-md);
        }
        
        .detail-row {
            grid-template-columns: 1fr;
            gap: var(--harmoni-spacing-sm);
        }
        
        .detail-row label {
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }
</style>

@code {
    private List<AuditEntry> auditEntries = new();
    private List<AuditEntry> filteredEntries = new();
    private AuditEntry? selectedEntry;
    private bool showDetailsModal = false;
    private bool isLoading = true;
    
    // Filtering
    private DateTime startDate = DateTime.Today.AddDays(-7);
    private DateTime endDate = DateTime.Today;
    private string selectedEventType = string.Empty;
    private string selectedSeverity = string.Empty;
    private string userFilter = string.Empty;
    private string ipFilter = string.Empty;
    private string resourceFilter = string.Empty;
    
    // Sorting
    private string sortField = nameof(AuditEntry.Timestamp);
    private bool sortDescending = true;
    
    // Pagination
    private int currentPage = 1;
    private int pageSize = 20;
    private int totalPages => (int)Math.Ceiling((double)filteredEntries.Count / pageSize);

    protected override async Task OnInitializedAsync()
    {
        await LoadAuditData();
    }

    private async Task LoadAuditData()
    {
        isLoading = true;
        
        // Simulate API call delay
        await Task.Delay(1000);
        
        // Generate mock audit data
        auditEntries = GenerateMockAuditData();
        filteredEntries = auditEntries.ToList();
        ApplySorting();
        
        isLoading = false;
    }

    private async Task ApplyFilters()
    {
        isLoading = true;
        await Task.Delay(500); // Simulate processing
        
        filteredEntries = auditEntries.Where(entry =>
        {
            if (!string.IsNullOrEmpty(selectedEventType) && entry.EventType != selectedEventType)
                return false;
                
            if (!string.IsNullOrEmpty(selectedSeverity) && entry.Severity != selectedSeverity)
                return false;
                
            if (!string.IsNullOrEmpty(userFilter) && !entry.User.Contains(userFilter, StringComparison.OrdinalIgnoreCase))
                return false;
                
            if (!string.IsNullOrEmpty(ipFilter) && !entry.IpAddress.Contains(ipFilter))
                return false;
                
            if (!string.IsNullOrEmpty(resourceFilter) && !entry.Resource.Contains(resourceFilter, StringComparison.OrdinalIgnoreCase))
                return false;
                
            if (entry.Timestamp.Date < startDate.Date)
                return false;
                
            if (entry.Timestamp.Date > endDate.Date)
                return false;
                
            return true;
        }).ToList();
        
        ApplySorting();
        currentPage = 1;
        isLoading = false;
    }

    private void ClearFilters()
    {
        startDate = DateTime.Today.AddDays(-7);
        endDate = DateTime.Today;
        selectedEventType = string.Empty;
        selectedSeverity = string.Empty;
        userFilter = string.Empty;
        ipFilter = string.Empty;
        resourceFilter = string.Empty;
        
        filteredEntries = auditEntries.ToList();
        ApplySorting();
        currentPage = 1;
    }

    private void SortBy(string field)
    {
        if (sortField == field)
        {
            sortDescending = !sortDescending;
        }
        else
        {
            sortField = field;
            sortDescending = true;
        }
        
        ApplySorting();
    }

    private void ApplySorting()
    {
        filteredEntries = sortField switch
        {
            nameof(AuditEntry.Timestamp) => sortDescending 
                ? filteredEntries.OrderByDescending(x => x.Timestamp).ToList()
                : filteredEntries.OrderBy(x => x.Timestamp).ToList(),
            nameof(AuditEntry.EventType) => sortDescending 
                ? filteredEntries.OrderByDescending(x => x.EventType).ToList()
                : filteredEntries.OrderBy(x => x.EventType).ToList(),
            nameof(AuditEntry.Severity) => sortDescending 
                ? filteredEntries.OrderByDescending(x => x.Severity).ToList()
                : filteredEntries.OrderBy(x => x.Severity).ToList(),
            nameof(AuditEntry.User) => sortDescending 
                ? filteredEntries.OrderByDescending(x => x.User).ToList()
                : filteredEntries.OrderBy(x => x.User).ToList(),
            nameof(AuditEntry.Action) => sortDescending 
                ? filteredEntries.OrderByDescending(x => x.Action).ToList()
                : filteredEntries.OrderBy(x => x.Action).ToList(),
            _ => filteredEntries
        };
    }

    private string GetSortIndicator(string field)
    {
        if (sortField != field) return "⇅";
        return sortDescending ? "▼" : "▲";
    }

    private IEnumerable<AuditEntry> GetPagedEntries()
    {
        return filteredEntries
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize);
    }

    private void PreviousPage()
    {
        if (currentPage > 1)
            currentPage--;
    }

    private void NextPage()
    {
        if (currentPage < totalPages)
            currentPage++;
    }

    private void ShowDetails(AuditEntry entry)
    {
        selectedEntry = entry;
        showDetailsModal = true;
    }

    private void CloseDetailsModal()
    {
        showDetailsModal = false;
        selectedEntry = null;
    }

    private async Task RefreshData()
    {
        await LoadAuditData();
        await ApplyFilters();
    }

    private async Task ExportAuditLog()
    {
        // TODO: Implement export functionality
        await JSRuntime.InvokeVoidAsync("alert", "Export functionality will be implemented soon.");
    }

    private string GetEventIcon(string eventType)
    {
        return eventType switch
        {
            "Authentication" => "🔐",
            "Authorization" => "🔑",
            "DataAccess" => "👁️",
            "DataModification" => "✏️",
            "SystemConfiguration" => "⚙️",
            "UserManagement" => "👤",
            "IncidentManagement" => "🚨",
            "HazardReporting" => "⚠️",
            _ => "📝"
        };
    }

    private string GetRowClass(string severity)
    {
        return severity.ToLower();
    }

    private string TruncateText(string text, int maxLength)
    {
        if (string.IsNullOrEmpty(text) || text.Length <= maxLength)
            return text;
            
        return text.Substring(0, maxLength) + "...";
    }

    private List<AuditEntry> GenerateMockAuditData()
    {
        var entries = new List<AuditEntry>();
        var random = new Random();
        var eventTypes = new[] { "Authentication", "Authorization", "DataAccess", "DataModification", "SystemConfiguration", "UserManagement", "IncidentManagement", "HazardReporting" };
        var severities = new[] { "Low", "Medium", "High", "Critical" };
        var users = new[] { "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" };
        var ips = new[] { "*************", "*********", "***********", "*************", "*********" };
        var resources = new[] { "User Management", "Incident Reports", "Hazard Assessments", "Training Records", "Audit Logs", "System Configuration", "Document Management" };

        for (int i = 0; i < 150; i++)
        {
            var eventType = eventTypes[random.Next(eventTypes.Length)];
            var severity = severities[random.Next(severities.Length)];
            var user = users[random.Next(users.Length)];
            var ip = ips[random.Next(ips.Length)];
            var resource = resources[random.Next(resources.Length)];
            
            entries.Add(new AuditEntry
            {
                Id = Guid.NewGuid().ToString(),
                Timestamp = DateTime.UtcNow.AddDays(-random.Next(30)).AddHours(-random.Next(24)).AddMinutes(-random.Next(60)),
                EventType = eventType,
                Severity = severity,
                User = user,
                IpAddress = ip,
                UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                Resource = resource,
                Action = GenerateAction(eventType),
                Details = GenerateDetails(eventType, user, resource),
                AdditionalData = random.Next(10) < 3 ? GenerateAdditionalData() : null
            });
        }

        return entries.OrderByDescending(x => x.Timestamp).ToList();
    }

    private string GenerateAction(string eventType)
    {
        return eventType switch
        {
            "Authentication" => "User login successful",
            "Authorization" => "Permission denied",
            "DataAccess" => "Record viewed",
            "DataModification" => "Record updated",
            "SystemConfiguration" => "Setting changed",
            "UserManagement" => "User created",
            "IncidentManagement" => "Incident reported",
            "HazardReporting" => "Hazard identified",
            _ => "Action performed"
        };
    }

    private string GenerateDetails(string eventType, string user, string resource)
    {
        return eventType switch
        {
            "Authentication" => $"User {user} successfully authenticated from new location",
            "Authorization" => $"User {user} attempted to access {resource} without sufficient permissions",
            "DataAccess" => $"User {user} accessed {resource} records for viewing",
            "DataModification" => $"User {user} modified {resource} record with ID #12345",
            "SystemConfiguration" => $"User {user} changed system configuration for {resource}",
            "UserManagement" => $"User {user} created new user <NAME_EMAIL>",
            "IncidentManagement" => $"User {user} reported new incident in Building A - Laboratory",
            "HazardReporting" => $"User {user} identified potential safety hazard in gymnasium area",
            _ => $"User {user} performed action on {resource}"
        };
    }

    private string GenerateAdditionalData()
    {
        return @"{
  ""sessionId"": ""sess_abc123def456"",
  ""requestId"": ""req_789xyz012"",
  ""correlationId"": ""corr_345ghi678"",
  ""module"": ""UserManagement"",
  ""version"": ""1.0.0""
}";
    }

    public class AuditEntry
    {
        public string Id { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string EventType { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public string User { get; set; } = string.Empty;
        public string IpAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
        public string Resource { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public string? AdditionalData { get; set; }
    }
}