// HarmoniHSE360 Ant Design Overrides
// Customizations to align Ant Design with HarmoniHSE360 brand

// ============================================
// Ant Design Color Overrides
// ============================================
.ant-btn-primary {
  background-color: var(--harmoni-btn-primary-bg);
  border-color: var(--harmoni-btn-primary-bg);
  
  &:hover {
    background-color: var(--harmoni-btn-primary-hover);
    border-color: var(--harmoni-btn-primary-hover);
  }
  
  &:focus {
    background-color: var(--harmoni-btn-primary-bg);
    border-color: var(--harmoni-btn-primary-bg);
    box-shadow: 0 0 0 2px var(--harmoni-input-focus-shadow);
  }
}

.ant-btn-default {
  color: var(--harmoni-text-primary);
  background-color: var(--harmoni-bg-secondary);
  border-color: var(--harmoni-border-color);
  
  &:hover {
    color: var(--harmoni-btn-primary-bg);
    border-color: var(--harmoni-btn-primary-bg);
    background-color: var(--harmoni-hover-bg);
  }
}

// ============================================
// Input Overrides
// ============================================
.ant-input,
.ant-input-affix-wrapper,
.ant-select-selector {
  background-color: var(--harmoni-input-bg);
  border-color: var(--harmoni-input-border);
  color: var(--harmoni-text-primary);
  
  &:hover {
    border-color: var(--harmoni-btn-primary-bg);
  }
  
  &:focus,
  &.ant-input-focused {
    border-color: var(--harmoni-input-focus-border);
    box-shadow: 0 0 0 2px var(--harmoni-input-focus-shadow);
  }
}

.ant-input::placeholder {
  color: var(--harmoni-text-secondary);
}

// ============================================
// Card Overrides
// ============================================
.ant-card {
  background-color: var(--harmoni-card-bg);
  border: var(--harmoni-card-border);
  box-shadow: var(--harmoni-card-shadow);
  color: var(--harmoni-text-primary);
  
  .ant-card-head {
    border-bottom-color: var(--harmoni-border-color);
    color: var(--harmoni-text-primary);
  }
  
  .ant-card-body {
    color: var(--harmoni-text-primary);
  }
}

// ============================================
// Table Overrides
// ============================================
.ant-table {
  background-color: var(--harmoni-table-bg);
  color: var(--harmoni-text-primary);
  
  .ant-table-thead > tr > th {
    background-color: var(--harmoni-bg-secondary);
    border-bottom-color: var(--harmoni-table-border);
    color: var(--harmoni-text-primary);
    font-weight: $harmoni-font-weight-semibold;
  }
  
  .ant-table-tbody > tr {
    &:hover > td {
      background-color: var(--harmoni-table-hover);
    }
    
    > td {
      border-bottom-color: var(--harmoni-table-border);
    }
    
    &:nth-child(even) {
      background-color: var(--harmoni-table-stripe);
    }
  }
}

// ============================================
// Form Overrides
// ============================================
.ant-form-item-label > label {
  color: var(--harmoni-text-primary);
  font-weight: $harmoni-font-weight-medium;
}

.ant-form-item-explain-error {
  color: var(--harmoni-error);
}

// ============================================
// Select Overrides
// ============================================
.ant-select-dropdown {
  background-color: var(--harmoni-card-bg);
  box-shadow: var(--harmoni-card-shadow);
  
  .ant-select-item {
    color: var(--harmoni-text-primary);
    
    &:hover {
      background-color: var(--harmoni-hover-bg);
    }
    
    &-option-selected:not(.ant-select-item-option-disabled) {
      background-color: var(--harmoni-selected-bg);
      color: var(--harmoni-btn-primary-bg);
      font-weight: $harmoni-font-weight-medium;
    }
  }
}

// ============================================
// Modal Overrides
// ============================================
.ant-modal {
  .ant-modal-content {
    background-color: var(--harmoni-card-bg);
    box-shadow: var(--harmoni-card-shadow);
  }
  
  .ant-modal-header {
    background-color: var(--harmoni-card-bg);
    border-bottom-color: var(--harmoni-border-color);
    
    .ant-modal-title {
      color: var(--harmoni-text-primary);
      font-weight: $harmoni-font-weight-semibold;
    }
  }
  
  .ant-modal-body {
    color: var(--harmoni-text-primary);
  }
  
  .ant-modal-footer {
    border-top-color: var(--harmoni-border-color);
  }
}

// ============================================
// Alert Overrides
// ============================================
.ant-alert {
  &-success {
    background-color: rgba($harmoni-success, 0.1);
    border-color: $harmoni-success;
    
    .ant-alert-icon {
      color: $harmoni-success;
    }
  }
  
  &-info {
    background-color: rgba($harmoni-info, 0.1);
    border-color: $harmoni-info;
    
    .ant-alert-icon {
      color: $harmoni-info;
    }
  }
  
  &-warning {
    background-color: rgba($harmoni-warning, 0.1);
    border-color: $harmoni-warning;
    
    .ant-alert-icon {
      color: $harmoni-warning;
    }
  }
  
  &-error {
    background-color: rgba($harmoni-error, 0.1);
    border-color: $harmoni-error;
    
    .ant-alert-icon {
      color: $harmoni-error;
    }
  }
}

// ============================================
// Menu Overrides
// ============================================
.ant-menu {
  background-color: var(--harmoni-nav-bg);
  color: var(--harmoni-nav-link);
  
  .ant-menu-item {
    color: var(--harmoni-nav-link);
    
    &:hover {
      color: var(--harmoni-nav-link-hover);
    }
    
    &-selected {
      color: var(--harmoni-nav-link-active);
      background-color: var(--harmoni-selected-bg);
      
      &::after {
        border-right-color: var(--harmoni-nav-link-active);
      }
    }
  }
  
  &-dark {
    background-color: var(--harmoni-sidebar-bg);
    
    .ant-menu-item {
      color: var(--harmoni-sidebar-text);
      
      &:hover {
        background-color: var(--harmoni-sidebar-hover);
      }
      
      &-selected {
        background-color: var(--harmoni-sidebar-active);
        color: white;
      }
    }
  }
}

// ============================================
// Tabs Overrides
// ============================================
.ant-tabs {
  .ant-tabs-tab {
    color: var(--harmoni-text-secondary);
    
    &:hover {
      color: var(--harmoni-btn-primary-bg);
    }
    
    &-active {
      color: var(--harmoni-btn-primary-bg);
      
      .ant-tabs-tab-btn {
        color: var(--harmoni-btn-primary-bg);
        font-weight: $harmoni-font-weight-medium;
      }
    }
  }
  
  .ant-tabs-ink-bar {
    background-color: var(--harmoni-btn-primary-bg);
  }
  
  .ant-tabs-nav {
    &::before {
      border-bottom-color: var(--harmoni-border-color);
    }
  }
}

// ============================================
// Checkbox & Radio Overrides
// ============================================
.ant-checkbox-checked {
  .ant-checkbox-inner {
    background-color: var(--harmoni-btn-primary-bg);
    border-color: var(--harmoni-btn-primary-bg);
  }
}

.ant-radio-checked {
  .ant-radio-inner {
    border-color: var(--harmoni-btn-primary-bg);
    
    &::after {
      background-color: var(--harmoni-btn-primary-bg);
    }
  }
}

// ============================================
// Typography Overrides
// ============================================
.ant-typography {
  color: var(--harmoni-text-primary);
  
  &.ant-typography-secondary {
    color: var(--harmoni-text-secondary);
  }
  
  a {
    color: var(--harmoni-btn-primary-bg);
    
    &:hover {
      color: var(--harmoni-btn-primary-hover);
    }
  }
}