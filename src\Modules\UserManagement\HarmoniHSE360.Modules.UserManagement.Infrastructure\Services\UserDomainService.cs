using HarmoniHSE360.Modules.UserManagement.Application.Contracts;
using HarmoniHSE360.Modules.UserManagement.Domain.Users;

namespace HarmoniHSE360.Modules.UserManagement.Infrastructure.Services;

public sealed class UserDomainService : IUserDomainService
{
    private readonly IUserRepository _userRepository;

    public UserDomainService(IUserRepository userRepository)
    {
        _userRepository = userRepository;
    }

    public async Task<User> CreateUserAsync(
        string email,
        string passwordHash,
        string firstName,
        string lastName,
        string? phoneNumber = null,
        AuthenticationSource authenticationSource = AuthenticationSource.Local,
        string? externalId = null,
        string? department = null,
        string? position = null)
    {
        await ValidateUserCreationAsync(email, externalId);

        var user = User.Create(
            email,
            firstName,
            lastName,
            phoneNumber,
            department,
            position,
            null, // employeeId
            authenticationSource,
            externalId);
        
        user.SetPasswordHash(passwordHash);
        return user;
    }

    public async Task ValidateUserCreationAsync(string email, string? externalId = null)
    {
        var existingUser = await _userRepository.GetByEmailAsync(email);
        if (existingUser is not null)
        {
            throw new InvalidOperationException($"User with email '{email}' already exists");
        }

        if (!string.IsNullOrEmpty(externalId))
        {
            var existingExternalUser = await _userRepository.GetByExternalIdAsync(externalId);
            if (existingExternalUser is not null)
            {
                throw new InvalidOperationException($"User with external ID '{externalId}' already exists");
            }
        }
    }
}