using HarmoniHSE360.Modules.UserManagement.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace HarmoniHSE360.Modules.UserManagement.Infrastructure;

public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<UserManagementDbContext>
{
    public UserManagementDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<UserManagementDbContext>();
        
        // Use the default connection string for migrations
        var connectionString = "Host=localhost;Port=5432;Database=harmonihse360_dev;Username=harmonihse360;Password=******************";
        
        optionsBuilder.UseNpgsql(connectionString, options =>
        {
            options.MigrationsHistoryTable("__EFMigrationsHistory", "user_management");
        });

        return new UserManagementDbContext(optionsBuilder.Options);
    }
}