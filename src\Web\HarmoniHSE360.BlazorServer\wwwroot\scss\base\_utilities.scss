// HarmoniHSE360 Utility Classes
// Common utility classes for rapid development

// ============================================
// Display Utilities
// ============================================
.d-none {
  display: none !important;
}

.d-block {
  display: block !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

.d-grid {
  display: grid !important;
}

// ============================================
// Flexbox Utilities
// ============================================
.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.justify-start {
  justify-content: flex-start !important;
}

.justify-end {
  justify-content: flex-end !important;
}

.justify-center {
  justify-content: center !important;
}

.justify-between {
  justify-content: space-between !important;
}

.justify-around {
  justify-content: space-around !important;
}

.justify-evenly {
  justify-content: space-evenly !important;
}

.align-start {
  align-items: flex-start !important;
}

.align-end {
  align-items: flex-end !important;
}

.align-center {
  align-items: center !important;
}

.align-baseline {
  align-items: baseline !important;
}

.align-stretch {
  align-items: stretch !important;
}

.flex-1 {
  flex: 1 1 0% !important;
}

.flex-auto {
  flex: 1 1 auto !important;
}

.flex-none {
  flex: none !important;
}

// ============================================
// Gap Utilities
// ============================================
@each $key, $value in (
  'xs': $harmoni-spacing-xs,
  'sm': $harmoni-spacing-sm,
  'md': $harmoni-spacing-md,
  'lg': $harmoni-spacing-lg,
  'xl': $harmoni-spacing-xl,
  '2xl': $harmoni-spacing-2xl
) {
  .gap-#{$key} {
    gap: $value !important;
  }
}

// ============================================
// Spacing Utilities
// ============================================
// Generate margin and padding utilities
@each $key, $value in (
  '0': 0,
  'xs': $harmoni-spacing-xs,
  'sm': $harmoni-spacing-sm,
  'md': $harmoni-spacing-md,
  'lg': $harmoni-spacing-lg,
  'xl': $harmoni-spacing-xl,
  '2xl': $harmoni-spacing-2xl,
  '3xl': $harmoni-spacing-3xl,
  'auto': auto
) {
  // Margin
  .m-#{$key} { margin: $value !important; }
  .mt-#{$key} { margin-top: $value !important; }
  .mr-#{$key} { margin-right: $value !important; }
  .mb-#{$key} { margin-bottom: $value !important; }
  .ml-#{$key} { margin-left: $value !important; }
  .mx-#{$key} { 
    margin-left: $value !important;
    margin-right: $value !important;
  }
  .my-#{$key} { 
    margin-top: $value !important;
    margin-bottom: $value !important;
  }
  
  // Padding (exclude auto)
  @if $key != 'auto' {
    .p-#{$key} { padding: $value !important; }
    .pt-#{$key} { padding-top: $value !important; }
    .pr-#{$key} { padding-right: $value !important; }
    .pb-#{$key} { padding-bottom: $value !important; }
    .pl-#{$key} { padding-left: $value !important; }
    .px-#{$key} { 
      padding-left: $value !important;
      padding-right: $value !important;
    }
    .py-#{$key} { 
      padding-top: $value !important;
      padding-bottom: $value !important;
    }
  }
}

// ============================================
// Width & Height Utilities
// ============================================
.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.min-h-screen {
  min-height: 100vh !important;
}

// ============================================
// Position Utilities
// ============================================
.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: sticky !important;
  top: 0;
}

// Position helpers
.top-0 { top: 0 !important; }
.right-0 { right: 0 !important; }
.bottom-0 { bottom: 0 !important; }
.left-0 { left: 0 !important; }

// ============================================
// Overflow Utilities
// ============================================
.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.overflow-visible {
  overflow: visible !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

.overflow-x-auto {
  overflow-x: auto !important;
}

.overflow-y-auto {
  overflow-y: auto !important;
}

// ============================================
// Border Utilities
// ============================================
.border-0 {
  border: 0 !important;
}

.border {
  border: 1px solid $harmoni-border-grey !important;
}

.border-top {
  border-top: 1px solid $harmoni-border-grey !important;
}

.border-right {
  border-right: 1px solid $harmoni-border-grey !important;
}

.border-bottom {
  border-bottom: 1px solid $harmoni-border-grey !important;
}

.border-left {
  border-left: 1px solid $harmoni-border-grey !important;
}

// Border radius
@each $key, $value in (
  'none': 0,
  'sm': $harmoni-radius-sm,
  'md': $harmoni-radius-md,
  'lg': $harmoni-radius-lg,
  'xl': $harmoni-radius-xl,
  'full': $harmoni-radius-full
) {
  .rounded-#{$key} {
    border-radius: $value !important;
  }
}

// ============================================
// Shadow Utilities
// ============================================
.shadow-none {
  box-shadow: none !important;
}

.shadow-sm {
  box-shadow: $harmoni-shadow-sm !important;
}

.shadow-md {
  box-shadow: $harmoni-shadow-md !important;
}

.shadow-lg {
  box-shadow: $harmoni-shadow-lg !important;
}

.shadow-xl {
  box-shadow: $harmoni-shadow-xl !important;
}

// ============================================
// Background Utilities
// ============================================
.bg-transparent {
  background-color: transparent !important;
}

.bg-white {
  background-color: white !important;
}

.bg-primary {
  background-color: $harmoni-teal-primary !important;
}

.bg-secondary {
  background-color: $harmoni-deep-blue !important;
}

.bg-success {
  background-color: $harmoni-success !important;
}

.bg-warning {
  background-color: $harmoni-warning !important;
}

.bg-error {
  background-color: $harmoni-error !important;
}

.bg-info {
  background-color: $harmoni-info !important;
}

.bg-light {
  background-color: $harmoni-soft-white !important;
}

.bg-gradient-primary {
  @include gradient-primary;
}

.bg-gradient-light {
  @include gradient-light;
}

// ============================================
// Cursor Utilities
// ============================================
.cursor-pointer {
  cursor: pointer !important;
}

.cursor-not-allowed {
  cursor: not-allowed !important;
}

.cursor-default {
  cursor: default !important;
}

// ============================================
// User Select
// ============================================
.select-none {
  user-select: none !important;
}

.select-text {
  user-select: text !important;
}

.select-all {
  user-select: all !important;
}

// ============================================
// Visibility
// ============================================
.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

// ============================================
// Opacity
// ============================================
.opacity-0 {
  opacity: 0 !important;
}

.opacity-50 {
  opacity: 0.5 !important;
}

.opacity-75 {
  opacity: 0.75 !important;
}

.opacity-100 {
  opacity: 1 !important;
}