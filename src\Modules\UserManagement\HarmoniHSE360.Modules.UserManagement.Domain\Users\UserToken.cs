using HarmoniHSE360.BuildingBlocks.Domain;

namespace HarmoniHSE360.Modules.UserManagement.Domain.Users;

public sealed class UserToken : Entity<Guid>
{
    public Guid UserId { get; private set; }
    public TokenType TokenType { get; private set; }
    public string TokenValue { get; private set; }
    public string Purpose { get; private set; }
    public DateTime ExpiresAt { get; private set; }
    public bool IsUsed { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UsedAt { get; private set; }

    private UserToken() { }

    private UserToken(Guid userId, TokenType tokenType, string tokenValue, string purpose, DateTime expiresAt)
    {
        Id = Guid.NewGuid();
        UserId = userId;
        TokenType = tokenType;
        TokenValue = tokenValue ?? throw new ArgumentNullException(nameof(tokenValue));
        Purpose = purpose ?? throw new ArgumentNullException(nameof(purpose));
        ExpiresAt = expiresAt;
        IsUsed = false;
        CreatedAt = DateTime.UtcNow;
    }

    public static UserToken Create(Guid userId, TokenType tokenType, string tokenValue, string purpose, DateTime expiresAt)
    {
        return new UserToken(userId, tokenType, tokenValue, purpose, expiresAt);
    }

    public void MarkAsUsed()
    {
        IsUsed = true;
        UsedAt = DateTime.UtcNow;
    }

    public bool IsExpired() => DateTime.UtcNow > ExpiresAt;
    public bool IsValid() => !IsUsed && !IsExpired();
}

public enum TokenType
{
    EmailConfirmation,
    PasswordReset,
    TwoFactorAuthentication,
    RefreshToken,
    ApiKey
}