using HarmoniHSE360.BuildingBlocks.Domain;

namespace HarmoniHSE360.Modules.UserManagement.Domain.Users;

public sealed class User : Entity<Guid>, IAggregateRoot
{
    private readonly List<UserRole> _userRoles = new();

    public string Email { get; private set; }
    public string NormalizedEmail { get; private set; }
    public string PasswordHash { get; private set; }
    public string FirstName { get; private set; }
    public string LastName { get; private set; }
    public string? PhoneNumber { get; private set; }
    public string? Department { get; private set; }
    public string? Position { get; private set; }
    public string? EmployeeId { get; private set; }
    public bool IsActive { get; private set; }
    public bool EmailConfirmed { get; private set; }
    public bool TwoFactorEnabled { get; private set; }
    public AuthenticationSource AuthenticationSource { get; private set; }
    public string? ExternalId { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public DateTime? LastLoginAt { get; private set; }

    public IReadOnlyCollection<UserRole> UserRoles => _userRoles.AsReadOnly();

    private User() { }

    private User(
        string email,
        string passwordHash,
        string firstName,
        string lastName,
        string? phoneNumber,
        AuthenticationSource authenticationSource,
        string? externalId,
        string? department,
        string? position,
        string? employeeId)
    {
        Id = Guid.NewGuid();
        Email = email ?? throw new ArgumentNullException(nameof(email));
        NormalizedEmail = email.ToUpperInvariant();
        PasswordHash = passwordHash ?? throw new ArgumentNullException(nameof(passwordHash));
        FirstName = firstName ?? throw new ArgumentNullException(nameof(firstName));
        LastName = lastName ?? throw new ArgumentNullException(nameof(lastName));
        PhoneNumber = phoneNumber;
        Department = department;
        Position = position;
        EmployeeId = employeeId;
        AuthenticationSource = authenticationSource;
        ExternalId = externalId;
        IsActive = true;
        EmailConfirmed = authenticationSource != AuthenticationSource.Local;
        TwoFactorEnabled = false;
        CreatedAt = DateTime.UtcNow;
    }

    public static User Create(
        string email,
        string firstName,
        string lastName,
        string? phoneNumber = null,
        string? department = null,
        string? position = null,
        string? employeeId = null,
        AuthenticationSource authenticationSource = AuthenticationSource.Local,
        string? externalId = null)
    {
        return new User(
            email,
            string.Empty, // Password hash will be set later
            firstName,
            lastName,
            phoneNumber,
            authenticationSource,
            externalId,
            department,
            position,
            employeeId);
    }

    public void UpdateLastLoginDate()
    {
        LastLoginAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }

    public void ConfirmEmail()
    {
        EmailConfirmed = true;
        UpdatedAt = DateTime.UtcNow;
    }

    public void EnableTwoFactor()
    {
        TwoFactorEnabled = true;
        UpdatedAt = DateTime.UtcNow;
    }

    public void DisableTwoFactor()
    {
        TwoFactorEnabled = false;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Deactivate()
    {
        IsActive = false;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        IsActive = true;
        UpdatedAt = DateTime.UtcNow;
    }

    public void UpdateProfile(string firstName, string lastName, string? phoneNumber, string? department, string? position)
    {
        FirstName = firstName ?? throw new ArgumentNullException(nameof(firstName));
        LastName = lastName ?? throw new ArgumentNullException(nameof(lastName));
        PhoneNumber = phoneNumber;
        Department = department;
        Position = position;
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddRole(UserRole userRole)
    {
        if (_userRoles.Any(ur => ur.RoleId == userRole.RoleId))
            return;

        _userRoles.Add(userRole);
        UpdatedAt = DateTime.UtcNow;
    }

    public void RemoveRole(Guid roleId)
    {
        _userRoles.RemoveAll(ur => ur.RoleId == roleId);
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetPasswordHash(string passwordHash)
    {
        PasswordHash = passwordHash ?? throw new ArgumentNullException(nameof(passwordHash));
        UpdatedAt = DateTime.UtcNow;
    }
}