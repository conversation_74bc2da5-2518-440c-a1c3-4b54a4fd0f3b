﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL.NodaTime" Version="8.0.0" />
    <PackageReference Include="EFCore.NamingConventions" Version="8.0.0" />
    <PackageReference Include="Polly" Version="8.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\HarmoniHSE360.BuildingBlocks.Application\HarmoniHSE360.BuildingBlocks.Application.csproj" />
    <ProjectReference Include="..\HarmoniHSE360.BuildingBlocks.Domain\HarmoniHSE360.BuildingBlocks.Domain.csproj" />
  </ItemGroup>

</Project>
