.twofa-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--harmoni-teal-primary) 0%, var(--harmoni-deep-blue) 100%);
    padding: 24px;
}

.twofa-wrapper {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 24px;
    width: 100%;
    max-width: 900px;
    align-items: start;
}

.twofa-card {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border-radius: var(--harmoni-radius-lg);
    border: none;
    overflow: hidden;
}

.brand-header {
    text-align: center;
    padding: 32px 0 24px;
    border-bottom: 1px solid var(--harmoni-border-grey);
    margin-bottom: 32px;
    background: linear-gradient(135deg, #f8fffe 0%, #e0f7fa 100%);
}

.security-icon-large {
    font-size: 48px;
    color: var(--harmoni-teal-primary);
    margin-bottom: 16px;
}

.security-title {
    color: var(--harmoni-teal-primary) !important;
    margin-bottom: 8px !important;
    font-family: var(--harmoni-font-secondary) !important;
}

.security-subtitle {
    font-size: 14px;
    color: var(--harmoni-charcoal);
}

.method-selection {
    padding: 0 24px 24px;
}

.method-card {
    border: 2px solid var(--harmoni-border-grey);
    transition: var(--harmoni-transition);
    cursor: pointer;
    border-radius: var(--harmoni-radius-md);
}

.method-card:hover {
    border-color: var(--harmoni-teal-primary);
}

.method-selected {
    border-color: var(--harmoni-teal-primary) !important;
    background-color: rgba(0, 151, 167, 0.05) !important;
}

.method-content {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 8px;
}

.method-icon {
    font-size: 24px;
    color: var(--harmoni-teal-primary);
    flex-shrink: 0;
}

.method-details {
    flex: 1;
}

.method-check {
    color: var(--harmoni-leaf-green);
    font-size: 20px;
}

.method-actions {
    margin-top: 32px;
    text-align: center;
}

.code-entry {
    padding: 0 24px 24px;
}

.code-instruction {
    text-align: center;
    margin-bottom: 32px;
}

.instruction-text {
    font-size: 16px;
    color: var(--harmoni-charcoal);
}

.code-input-group {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-bottom: 24px;
}

.code-digit {
    width: 50px !important;
    height: 50px !important;
    text-align: center !important;
    font-size: 20px !important;
    font-weight: 600 !important;
    border-radius: var(--harmoni-radius-md) !important;
}

.resend-section {
    text-align: center;
    margin-bottom: 24px;
}

.resend-countdown {
    font-size: 14px;
}

.security-info-card {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border-radius: var(--harmoni-radius-lg);
    border: none;
    background: linear-gradient(135deg, #f8fffe 0%, #e0f7fa 100%);
    position: sticky;
    top: 24px;
    border: 1px solid rgba(0, 151, 167, 0.2);
}

.info-title {
    color: var(--harmoni-teal-primary) !important;
    margin-bottom: 16px !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.info-icon {
    color: var(--harmoni-teal-primary);
    font-size: 18px;
}

.security-benefit {
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.benefit-icon {
    color: var(--harmoni-teal-primary);
    font-size: 18px;
    margin-top: 2px;
    flex-shrink: 0;
}

.benefit-content {
    flex: 1;
}

.benefit-description {
    font-size: 13px;
    line-height: 1.4;
}

.help-section {
    margin-top: 8px;
}

.help-text {
    font-size: 13px;
    margin: 0;
    line-height: 1.4;
}

/* Ant Design component customizations */
::deep .ant-btn-primary {
    background-color: var(--harmoni-teal-primary);
    border-color: var(--harmoni-teal-primary);
}

::deep .ant-btn-primary:hover {
    background-color: var(--harmoni-deep-blue);
    border-color: var(--harmoni-deep-blue);
}

::deep .ant-input:focus,
::deep .ant-input-focused {
    border-color: var(--harmoni-teal-primary);
    box-shadow: 0 0 0 2px rgba(0, 151, 167, 0.1);
}

::deep .ant-alert-error {
    border: 1px solid var(--harmoni-error);
    background-color: rgba(244, 67, 54, 0.05);
}

::deep .ant-alert-success {
    border: 1px solid var(--harmoni-success);
    background-color: rgba(76, 175, 80, 0.05);
}

/* Mobile responsive */
@media (max-width: 768px) {
    .twofa-wrapper {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .security-info-card {
        order: -1;
        position: static;
    }
    
    .brand-header {
        padding: 24px 0 16px;
    }
    
    .security-icon-large {
        font-size: 40px;
    }
    
    .method-selection,
    .code-entry {
        padding: 0 16px 16px;
    }
    
    .code-input-group {
        gap: 8px;
    }
    
    .code-digit {
        width: 45px !important;
        height: 45px !important;
        font-size: 18px !important;
    }
}

@media (max-width: 480px) {
    .twofa-container {
        padding: 16px;
    }
    
    .code-digit {
        width: 40px !important;
        height: 40px !important;
        font-size: 16px !important;
    }
    
    .method-selection,
    .code-entry {
        padding: 0 12px 12px;
    }
}