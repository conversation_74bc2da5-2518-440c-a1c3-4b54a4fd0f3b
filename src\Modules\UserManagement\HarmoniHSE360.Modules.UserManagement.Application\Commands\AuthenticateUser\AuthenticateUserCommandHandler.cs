using HarmoniHSE360.BuildingBlocks.Application.Commands;
using HarmoniHSE360.Modules.UserManagement.Application.Contracts;
using HarmoniHSE360.Modules.UserManagement.Domain.Users;

namespace HarmoniHSE360.Modules.UserManagement.Application.Commands.AuthenticateUser;

public sealed class AuthenticateUserCommandHandler : ICommandHandler<AuthenticateUserCommand, AuthenticationResult>
{
    private readonly IUserRepository _userRepository;
    private readonly IPasswordHasher _passwordHasher;
    private readonly ITokenService _tokenService;
    private readonly IUserLoginRepository _userLoginRepository;

    public AuthenticateUserCommandHandler(
        IUserRepository userRepository,
        IPasswordHasher passwordHasher,
        ITokenService tokenService,
        IUserLoginRepository userLoginRepository)
    {
        _userRepository = userRepository;
        _passwordHasher = passwordHasher;
        _tokenService = tokenService;
        _userLoginRepository = userLoginRepository;
    }

    public async Task<AuthenticationResult> Handle(AuthenticateUserCommand request, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);
        if (user is null)
        {
            throw new UnauthorizedAccessException("Invalid email or password");
        }

        if (!user.IsActive)
        {
            throw new UnauthorizedAccessException("User account is deactivated");
        }

        if (!_passwordHasher.VerifyPassword(request.Password, user.PasswordHash))
        {
            await RecordFailedLoginAttempt(user.Id, request.IpAddress, request.UserAgent, cancellationToken);
            throw new UnauthorizedAccessException("Invalid email or password");
        }

        var accessToken = _tokenService.GenerateAccessToken(user);
        var refreshToken = _tokenService.GenerateRefreshToken();

        await RecordSuccessfulLogin(user.Id, request.IpAddress, request.UserAgent, cancellationToken);

        user.UpdateLastLoginDate();
        await _userRepository.UpdateAsync(user, cancellationToken);

        return new AuthenticationResult(
            user.Id,
            accessToken,
            refreshToken,
            DateTime.UtcNow.AddHours(1),
            user.TwoFactorEnabled
        );
    }

    private async Task RecordSuccessfulLogin(Guid userId, string? ipAddress, string? userAgent, CancellationToken cancellationToken)
    {
        var login = UserLogin.Create(userId, ipAddress, userAgent, true);
        await _userLoginRepository.AddAsync(login, cancellationToken);
    }

    private async Task RecordFailedLoginAttempt(Guid userId, string? ipAddress, string? userAgent, CancellationToken cancellationToken)
    {
        var login = UserLogin.Create(userId, ipAddress, userAgent, false);
        await _userLoginRepository.AddAsync(login, cancellationToken);
    }
}