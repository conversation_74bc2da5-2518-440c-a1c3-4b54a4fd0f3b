// HarmoniHSE360 Typography System
// Consistent typography styles across the application

// ============================================
// Font Imports
// ============================================
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500&display=swap');

// ============================================
// Heading Styles
// ============================================
h1, .h1 {
  font-size: $harmoni-font-size-4xl;
  font-weight: $harmoni-font-weight-bold;
  letter-spacing: -0.02em;
  
  @include breakpoint(xs) {
    font-size: $harmoni-font-size-3xl;
  }
}

h2, .h2 {
  font-size: $harmoni-font-size-3xl;
  font-weight: $harmoni-font-weight-semibold;
  letter-spacing: -0.01em;
  
  @include breakpoint(xs) {
    font-size: $harmoni-font-size-2xl;
  }
}

h3, .h3 {
  font-size: $harmoni-font-size-2xl;
  font-weight: $harmoni-font-weight-semibold;
}

h4, .h4 {
  font-size: $harmoni-font-size-xl;
  font-weight: $harmoni-font-weight-medium;
}

h5, .h5 {
  font-size: $harmoni-font-size-lg;
  font-weight: $harmoni-font-weight-medium;
}

h6, .h6 {
  font-size: $harmoni-font-size-base;
  font-weight: $harmoni-font-weight-medium;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

// ============================================
// Body Text Styles
// ============================================
.text-xs {
  font-size: $harmoni-font-size-xs;
  line-height: $harmoni-line-height-normal;
}

.text-sm {
  font-size: $harmoni-font-size-sm;
  line-height: $harmoni-line-height-normal;
}

.text-base {
  font-size: $harmoni-font-size-base;
  line-height: $harmoni-line-height-normal;
}

.text-lg {
  font-size: $harmoni-font-size-lg;
  line-height: $harmoni-line-height-normal;
}

.text-xl {
  font-size: $harmoni-font-size-xl;
  line-height: $harmoni-line-height-normal;
}

// ============================================
// Font Weight Classes
// ============================================
.font-light {
  font-weight: $harmoni-font-weight-light;
}

.font-normal {
  font-weight: $harmoni-font-weight-normal;
}

.font-medium {
  font-weight: $harmoni-font-weight-medium;
}

.font-semibold {
  font-weight: $harmoni-font-weight-semibold;
}

.font-bold {
  font-weight: $harmoni-font-weight-bold;
}

// ============================================
// Text Color Classes
// ============================================
.text-primary {
  color: $harmoni-teal-primary;
}

.text-secondary {
  color: $harmoni-deep-blue;
}

.text-success {
  color: $harmoni-success;
}

.text-warning {
  color: $harmoni-warning;
}

.text-error {
  color: $harmoni-error;
}

.text-info {
  color: $harmoni-info;
}

.text-muted {
  color: $harmoni-cool-grey;
}

.text-white {
  color: white;
}

// ============================================
// Text Alignment
// ============================================
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

// ============================================
// Text Transform
// ============================================
.text-uppercase {
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.text-lowercase {
  text-transform: lowercase;
}

.text-capitalize {
  text-transform: capitalize;
}

// ============================================
// Special Typography
// ============================================
.lead {
  font-size: $harmoni-font-size-lg;
  font-weight: $harmoni-font-weight-light;
  line-height: $harmoni-line-height-relaxed;
}

.code {
  font-family: $harmoni-font-mono;
  font-size: 0.875em;
  padding: 0.2em 0.4em;
  background-color: rgba($harmoni-cool-grey, 0.1);
  border-radius: $harmoni-radius-sm;
  color: $harmoni-deep-blue;
}

.kbd {
  font-family: $harmoni-font-mono;
  font-size: 0.875em;
  padding: 0.2em 0.4em;
  background-color: $harmoni-charcoal;
  color: white;
  border-radius: $harmoni-radius-sm;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.2);
}

blockquote {
  margin: $harmoni-spacing-lg 0;
  padding-left: $harmoni-spacing-lg;
  border-left: 4px solid $harmoni-teal-primary;
  font-style: italic;
  color: darken($harmoni-cool-grey, 20%);
  
  cite {
    display: block;
    margin-top: $harmoni-spacing-sm;
    font-size: $harmoni-font-size-sm;
    font-style: normal;
    color: $harmoni-cool-grey;
    
    &::before {
      content: '— ';
    }
  }
}

// ============================================
// List Styles
// ============================================
.list-style-disc {
  list-style-type: disc;
  padding-left: $harmoni-spacing-lg;
  
  li {
    margin-bottom: $harmoni-spacing-xs;
  }
}

.list-style-decimal {
  list-style-type: decimal;
  padding-left: $harmoni-spacing-lg;
  
  li {
    margin-bottom: $harmoni-spacing-xs;
  }
}

// ============================================
// Truncation
// ============================================
.truncate {
  @include text-truncate;
}

.line-clamp-2 {
  @include line-clamp(2);
}

.line-clamp-3 {
  @include line-clamp(3);
}