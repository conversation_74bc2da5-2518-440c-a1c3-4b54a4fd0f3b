// HarmoniHSE360 JavaScript Utilities

window.harmoni = {
    // Focus element by ID
    focusElement: function (elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.focus();
        }
    },

    // Select text in element
    selectText: function (elementId) {
        const element = document.getElementById(elementId);
        if (element && element.select) {
            element.select();
        }
    },

    // Show notification toast
    showToast: function (message, type = 'info', duration = 3000) {
        // Remove existing toasts
        const existingToasts = document.querySelectorAll('.harmoni-toast');
        existingToasts.forEach(toast => toast.remove());

        // Create toast element
        const toast = document.createElement('div');
        toast.className = `harmoni-toast harmoni-toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <span class="toast-icon">${this.getToastIcon(type)}</span>
                <span class="toast-message">${message}</span>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // Add styles if not already added
        this.addToastStyles();

        // Add to page
        document.body.appendChild(toast);

        // Auto remove after duration
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, duration);

        // Animate in
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
    },

    // Get icon for toast type
    getToastIcon: function (type) {
        switch (type) {
            case 'success': return '✅';
            case 'warning': return '⚠️';
            case 'error': return '❌';
            case 'info':
            default: return 'ℹ️';
        }
    },

    // Add toast styles to page
    addToastStyles: function () {
        if (!document.getElementById('harmoni-toast-styles')) {
            const styles = document.createElement('style');
            styles.id = 'harmoni-toast-styles';
            styles.textContent = `
                .harmoni-toast {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    border-left: 4px solid #0097A7;
                    min-width: 300px;
                    max-width: 500px;
                    transform: translateX(100%);
                    transition: transform 0.3s ease-in-out;
                }
                
                .harmoni-toast.show {
                    transform: translateX(0);
                }
                
                .harmoni-toast-success {
                    border-left-color: #4CAF50;
                }
                
                .harmoni-toast-warning {
                    border-left-color: #FF9800;
                }
                
                .harmoni-toast-error {
                    border-left-color: #F44336;
                }
                
                .toast-content {
                    display: flex;
                    align-items: center;
                    padding: 16px;
                    gap: 12px;
                }
                
                .toast-icon {
                    font-size: 18px;
                    flex-shrink: 0;
                }
                
                .toast-message {
                    flex: 1;
                    font-family: 'Inter', sans-serif;
                    font-size: 14px;
                    line-height: 1.4;
                    color: #212121;
                }
                
                .toast-close {
                    background: none;
                    border: none;
                    font-size: 18px;
                    cursor: pointer;
                    color: #666;
                    padding: 0;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 4px;
                    transition: background-color 0.2s;
                }
                
                .toast-close:hover {
                    background-color: #f5f5f5;
                }
                
                @media (max-width: 600px) {
                    .harmoni-toast {
                        top: 10px;
                        right: 10px;
                        left: 10px;
                        min-width: auto;
                        max-width: none;
                    }
                }
            `;
            document.head.appendChild(styles);
        }
    },

    // Scroll to element smoothly
    scrollToElement: function (elementId, offset = 0) {
        const element = document.getElementById(elementId);
        if (element) {
            const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
            const offsetPosition = elementPosition - offset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });
        }
    },

    // Copy text to clipboard
    copyToClipboard: function (text) {
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(() => {
                this.showToast('Copied to clipboard', 'success', 2000);
            }).catch(() => {
                this.fallbackCopyToClipboard(text);
            });
        } else {
            this.fallbackCopyToClipboard(text);
        }
    },

    // Fallback copy method
    fallbackCopyToClipboard: function (text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            this.showToast('Copied to clipboard', 'success', 2000);
        } catch (err) {
            this.showToast('Failed to copy to clipboard', 'error', 3000);
        }

        document.body.removeChild(textArea);
    },

    // Detect if user is on mobile
    isMobile: function () {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    // Get browser info
    getBrowserInfo: function () {
        const userAgent = navigator.userAgent;
        let browserName = 'Unknown';
        let browserVersion = 'Unknown';

        if (userAgent.indexOf('Firefox') > -1) {
            browserName = 'Firefox';
            browserVersion = userAgent.substring(userAgent.indexOf('Firefox') + 8);
        } else if (userAgent.indexOf('SamsungBrowser') > -1) {
            browserName = 'Samsung Browser';
            browserVersion = userAgent.substring(userAgent.indexOf('SamsungBrowser') + 15);
        } else if (userAgent.indexOf('Opera') > -1 || userAgent.indexOf('OPR') > -1) {
            browserName = 'Opera';
            browserVersion = userAgent.indexOf('Opera') > -1 ? 
                userAgent.substring(userAgent.indexOf('Opera') + 6) : 
                userAgent.substring(userAgent.indexOf('OPR') + 4);
        } else if (userAgent.indexOf('Trident') > -1) {
            browserName = 'Internet Explorer';
            browserVersion = userAgent.substring(userAgent.indexOf('rv:') + 3);
        } else if (userAgent.indexOf('Edge') > -1) {
            browserName = 'Edge';
            browserVersion = userAgent.substring(userAgent.indexOf('Edge') + 5);
        } else if (userAgent.indexOf('Chrome') > -1) {
            browserName = 'Chrome';
            browserVersion = userAgent.substring(userAgent.indexOf('Chrome') + 7);
        } else if (userAgent.indexOf('Safari') > -1) {
            browserName = 'Safari';
            browserVersion = userAgent.substring(userAgent.indexOf('Safari') + 7);
        }

        if (browserVersion.indexOf(' ') > -1) {
            browserVersion = browserVersion.substring(0, browserVersion.indexOf(' '));
        }
        if (browserVersion.indexOf(';') > -1) {
            browserVersion = browserVersion.substring(0, browserVersion.indexOf(';'));
        }
        if (browserVersion.indexOf(')') > -1) {
            browserVersion = browserVersion.substring(0, browserVersion.indexOf(')'));
        }

        return {
            name: browserName,
            version: browserVersion,
            userAgent: userAgent,
            isMobile: this.isMobile()
        };
    },

    // Local storage helpers
    storage: {
        set: function (key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (e) {
                console.error('Failed to save to localStorage:', e);
                return false;
            }
        },

        get: function (key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (e) {
                console.error('Failed to read from localStorage:', e);
                return defaultValue;
            }
        },

        remove: function (key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (e) {
                console.error('Failed to remove from localStorage:', e);
                return false;
            }
        },

        clear: function () {
            try {
                localStorage.clear();
                return true;
            } catch (e) {
                console.error('Failed to clear localStorage:', e);
                return false;
            }
        }
    },

    // Theme management
    theme: {
        toggle: function () {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            this.set(newTheme);
        },

        set: function (theme) {
            document.documentElement.setAttribute('data-theme', theme);
            harmoni.storage.set('harmoni-theme', theme);
            
            // Dispatch theme change event
            window.dispatchEvent(new CustomEvent('harmoni-theme-changed', {
                detail: { theme: theme }
            }));
        },

        get: function () {
            return document.documentElement.getAttribute('data-theme') || 'light';
        },

        init: function () {
            const savedTheme = harmoni.storage.get('harmoni-theme', 'light');
            this.set(savedTheme);
        }
    },

    // Accessibility helpers
    a11y: {
        announceToScreenReader: function (message) {
            const announcement = document.createElement('div');
            announcement.setAttribute('aria-live', 'polite');
            announcement.setAttribute('aria-atomic', 'true');
            announcement.style.position = 'absolute';
            announcement.style.left = '-10000px';
            announcement.style.width = '1px';
            announcement.style.height = '1px';
            announcement.style.overflow = 'hidden';
            announcement.textContent = message;
            
            document.body.appendChild(announcement);
            
            setTimeout(() => {
                document.body.removeChild(announcement);
            }, 1000);
        },

        trapFocus: function (element) {
            const focusableElements = element.querySelectorAll(
                'a[href], button:not([disabled]), textarea:not([disabled]), input[type="text"]:not([disabled]), input[type="radio"]:not([disabled]), input[type="checkbox"]:not([disabled]), select:not([disabled])'
            );
            
            const firstElement = focusableElements[0];
            const lastElement = focusableElements[focusableElements.length - 1];

            element.addEventListener('keydown', function (e) {
                if (e.key === 'Tab') {
                    if (e.shiftKey) {
                        if (document.activeElement === firstElement) {
                            lastElement.focus();
                            e.preventDefault();
                        }
                    } else {
                        if (document.activeElement === lastElement) {
                            firstElement.focus();
                            e.preventDefault();
                        }
                    }
                }
            });

            // Focus first element
            if (firstElement) {
                firstElement.focus();
            }
        }
    }
};

// Initialize theme on page load
document.addEventListener('DOMContentLoaded', function () {
    harmoni.theme.init();
});

// Legacy function names for backward compatibility
window.focusElement = harmoni.focusElement;
window.selectText = harmoni.selectText;