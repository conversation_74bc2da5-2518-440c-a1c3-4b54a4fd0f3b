using HarmoniHSE360.BuildingBlocks.Domain;

namespace HarmoniHSE360.Modules.UserManagement.Domain.Users;

public sealed class Role : Entity<Guid>, IAggregateRoot
{
    private readonly List<RolePermission> _rolePermissions = new();

    public string Name { get; private set; }
    public string NormalizedName { get; private set; }
    public string? Description { get; private set; }
    public bool IsSystemRole { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }

    public IReadOnlyCollection<RolePermission> RolePermissions => _rolePermissions.AsReadOnly();

    private Role() { }

    private Role(string name, string? description, bool isSystemRole)
    {
        Id = Guid.NewGuid();
        Name = name ?? throw new ArgumentNullException(nameof(name));
        NormalizedName = name.ToUpperInvariant();
        Description = description;
        IsSystemRole = isSystemRole;
        CreatedAt = DateTime.UtcNow;
    }

    public static Role Create(string name, string? description = null, bool isSystemRole = false)
    {
        return new Role(name, description, isSystemRole);
    }

    public void UpdateDetails(string name, string? description)
    {
        if (IsSystemRole)
            throw new InvalidOperationException("Cannot modify system roles.");

        Name = name ?? throw new ArgumentNullException(nameof(name));
        NormalizedName = name.ToUpperInvariant();
        Description = description;
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddPermission(RolePermission rolePermission)
    {
        if (_rolePermissions.Any(rp => rp.PermissionId == rolePermission.PermissionId))
            return;

        _rolePermissions.Add(rolePermission);
        UpdatedAt = DateTime.UtcNow;
    }

    public void RemovePermission(Guid permissionId)
    {
        _rolePermissions.RemoveAll(rp => rp.PermissionId == permissionId);
        UpdatedAt = DateTime.UtcNow;
    }

    // System role factory methods
    public static Role SystemAdministrator()
        => new("SystemAdministrator", "Full system access", true);

    public static Role HSEManager()
        => new("HSEManager", "Comprehensive HSE functionality access", true);

    public static Role DepartmentHead()
        => new("DepartmentHead", "Department-specific management features", true);

    public static Role Employee()
        => new("Employee", "Standard HSE participation features", true);

    public static Role Contractor()
        => new("Contractor", "Limited access for relevant HSE activities", true);

    public static Role Student()
        => new("Student", "Restricted access for hazard reporting", true);

    public static Role Parent()
        => new("Parent", "View-only access for incident notifications", true);
}