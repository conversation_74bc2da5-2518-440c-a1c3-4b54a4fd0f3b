using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HarmoniHSE360.Modules.UserManagement.Domain.Users;

namespace HarmoniHSE360.Modules.UserManagement.Infrastructure.Persistence.Configurations;

public sealed class UserTokenConfiguration : IEntityTypeConfiguration<UserToken>
{
    public void Configure(EntityTypeBuilder<UserToken> builder)
    {
        builder.ToTable("user_tokens");

        builder.HasKey(ut => ut.Id);

        builder.Property(ut => ut.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(ut => ut.UserId)
            .HasColumnName("user_id")
            .IsRequired();

        builder.Property(ut => ut.TokenType)
            .HasColumnName("token_type")
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(ut => ut.TokenValue)
            .HasColumnName("token_value")
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(ut => ut.Purpose)
            .HasColumnName("purpose")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(ut => ut.ExpiresAt)
            .HasColumnName("expires_at")
            .IsRequired();

        builder.Property(ut => ut.IsUsed)
            .HasColumnName("is_used")
            .IsRequired();

        builder.Property(ut => ut.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(ut => ut.UsedAt)
            .HasColumnName("used_at");

        builder.HasOne<User>()
            .WithMany()
            .HasForeignKey(ut => ut.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(ut => ut.UserId)
            .HasDatabaseName("ix_user_tokens_user_id");

        builder.HasIndex(ut => ut.TokenValue)
            .IsUnique()
            .HasDatabaseName("ix_user_tokens_token_value");

        builder.HasIndex(ut => ut.ExpiresAt)
            .HasDatabaseName("ix_user_tokens_expires_at");

        builder.HasIndex(ut => new { ut.UserId, ut.TokenType, ut.Purpose })
            .HasDatabaseName("ix_user_tokens_user_type_purpose");
    }
}