using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace HarmoniHSE360.BlazorServer.Services;

public class CustomAuthenticationStateProvider : AuthenticationStateProvider
{
    private readonly IJSRuntime _jsRuntime;
    private ClaimsPrincipal _anonymous = new(new ClaimsIdentity());
    private ClaimsPrincipal? _currentUser;

    public CustomAuthenticationStateProvider(IJSRuntime jsRuntime)
    {
        _jsRuntime = jsRuntime;
    }

    public override async Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        if (_currentUser != null)
        {
            return new AuthenticationState(_currentUser);
        }

        // Try to restore authentication state from stored token
        try
        {
            var token = await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", "harmoni_access_token");
            if (!string.IsNullOrEmpty(token))
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var jwtToken = tokenHandler.ReadJwtToken(token);

                // Check if token is expired
                if (jwtToken.ValidTo > DateTime.UtcNow)
                {
                    var claims = jwtToken.Claims.ToList();
                    var authenticatedUser = new ClaimsPrincipal(new ClaimsIdentity(claims, "jwt"));
                    _currentUser = authenticatedUser;
                    return new AuthenticationState(authenticatedUser);
                }
                else
                {
                    // Token expired, clear it
                    await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "harmoni_access_token");
                    await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "harmoni_refresh_token");
                }
            }
        }
        catch
        {
            // If there's any error reading the token, continue as anonymous
        }

        return new AuthenticationState(_anonymous);
    }

    public void MarkUserAsAuthenticated(string email, string userId, string firstName, string lastName, List<string> roles)
    {
        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.Email, email),
            new Claim(ClaimTypes.NameIdentifier, userId),
            new Claim(ClaimTypes.Name, $"{firstName} {lastName}"),
            new Claim(ClaimTypes.GivenName, firstName),
            new Claim(ClaimTypes.Surname, lastName)
        };

        foreach (var role in roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role));
        }

        var authenticatedUser = new ClaimsPrincipal(new ClaimsIdentity(claims, "apiauth"));
        _currentUser = authenticatedUser;
        var authState = Task.FromResult(new AuthenticationState(authenticatedUser));
        NotifyAuthenticationStateChanged(authState);
    }

    public void MarkUserAsLoggedOut()
    {
        _currentUser = null;
        var authState = Task.FromResult(new AuthenticationState(_anonymous));
        NotifyAuthenticationStateChanged(authState);
    }
}