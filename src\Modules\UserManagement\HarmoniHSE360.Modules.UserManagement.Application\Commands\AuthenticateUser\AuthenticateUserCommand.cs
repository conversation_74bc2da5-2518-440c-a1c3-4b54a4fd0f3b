using HarmoniHSE360.BuildingBlocks.Application.Commands;

namespace HarmoniHSE360.Modules.UserManagement.Application.Commands.AuthenticateUser;

public sealed record AuthenticateUserCommand(
    string Email,
    string Password,
    string? IpAddress = null,
    string? UserAgent = null
) : ICommand<AuthenticationResult>;

public sealed record AuthenticationResult(
    Guid UserId,
    string AccessToken,
    string RefreshToken,
    DateTime ExpiresAt,
    bool RequiresTwoFactor = false
);