using HarmoniHSE360.Modules.UserManagement.Domain.Users;

namespace HarmoniHSE360.Modules.UserManagement.Application.Contracts;

public interface IUserDomainService
{
    Task<User> CreateUserAsync(
        string email,
        string passwordHash,
        string firstName,
        string lastName,
        string? phoneNumber = null,
        AuthenticationSource authenticationSource = AuthenticationSource.Local,
        string? externalId = null,
        string? department = null,
        string? position = null);

    Task ValidateUserCreationAsync(string email, string? externalId = null);
}