using HarmoniHSE360.BuildingBlocks.Domain;

namespace HarmoniHSE360.Modules.UserManagement.Domain.Users;

public sealed class UserLogin : Entity<Guid>
{
    public Guid UserId { get; private set; }
    public string? IpAddress { get; private set; }
    public string? UserAgent { get; private set; }
    public bool IsSuccessful { get; private set; }
    public DateTime AttemptedAt { get; private set; }

    private UserLogin() { }

    private UserLogin(Guid userId, string? ipAddress, string? userAgent, bool isSuccessful, DateTime attemptedAt)
    {
        Id = Guid.NewGuid();
        UserId = userId;
        IpAddress = ipAddress;
        UserAgent = userAgent;
        IsSuccessful = isSuccessful;
        AttemptedAt = attemptedAt;
    }

    public static UserLogin Create(Guid userId, string? ipAddress, string? userAgent, bool isSuccessful)
    {
        return new UserLogin(userId, ipAddress, userAgent, isSuccessful, DateTime.UtcNow);
    }
}