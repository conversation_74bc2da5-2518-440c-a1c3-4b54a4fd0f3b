using HarmoniHSE360.Modules.UserManagement.Domain.Users;
using HarmoniHSE360.Modules.UserManagement.Application.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace HarmoniHSE360.Modules.UserManagement.Infrastructure.Persistence.DataSeeding;

public class UserManagementDataSeeder
{
    private readonly UserManagementDbContext _context;
    private readonly IPasswordHasher _passwordHasher;
    private readonly ILogger<UserManagementDataSeeder> _logger;

    public UserManagementDataSeeder(
        UserManagementDbContext context, 
        IPasswordHasher passwordHasher,
        ILogger<UserManagementDataSeeder> logger)
    {
        _context = context;
        _passwordHasher = passwordHasher;
        _logger = logger;
    }

    public async Task SeedAsync()
    {
        try
        {
            _logger.LogInformation("Starting UserManagement data seeding...");

            // Ensure database is created
            await _context.Database.EnsureCreatedAsync();

            // Seed in order: Permissions -> Roles -> Users -> Role assignments -> Role permissions
            await SeedPermissionsAsync();
            await SeedRolesAsync();
            await SeedUsersAsync();
            await SeedUserRolesAsync();
            await SeedRolePermissionsAsync();

            await _context.SaveChangesAsync();
            _logger.LogInformation("UserManagement data seeding completed successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during UserManagement data seeding");
            throw;
        }
    }

    private async Task SeedPermissionsAsync()
    {
        if (await _context.Permissions.AnyAsync())
        {
            _logger.LogInformation("Permissions already exist, skipping permission seeding.");
            return;
        }

        _logger.LogInformation("Seeding permissions...");
        var permissions = DefaultPermissions.GetAllPermissions();
        
        await _context.Permissions.AddRangeAsync(permissions);
        await _context.SaveChangesAsync();
        
        _logger.LogInformation($"Seeded {permissions.Count} permissions.");
    }

    private async Task SeedRolesAsync()
    {
        if (await _context.Roles.AnyAsync())
        {
            _logger.LogInformation("Roles already exist, skipping role seeding.");
            return;
        }

        _logger.LogInformation("Seeding roles...");
        var roles = new List<Role>
        {
            Role.SystemAdministrator(),
            Role.HSEManager(),
            Role.DepartmentHead(),
            Role.Employee(),
            Role.Contractor(),
            Role.Student(),
            Role.Parent()
        };

        await _context.Roles.AddRangeAsync(roles);
        await _context.SaveChangesAsync();
        
        _logger.LogInformation($"Seeded {roles.Count} roles.");
    }

    private async Task SeedUsersAsync()
    {
        if (await _context.Users.AnyAsync())
        {
            _logger.LogInformation("Users already exist, skipping user seeding.");
            return;
        }

        _logger.LogInformation("Seeding users...");
        var users = DefaultUsers.GetDefaultUsers();
        
        // Set default passwords for all users
        DefaultUsers.SetDefaultPasswords(users, _passwordHasher.HashPassword);
        
        await _context.Users.AddRangeAsync(users);
        await _context.SaveChangesAsync();
        
        _logger.LogInformation($"Seeded {users.Count} users.");
    }

    private async Task SeedUserRolesAsync()
    {
        if (await _context.UserRoles.AnyAsync())
        {
            _logger.LogInformation("User roles already exist, skipping user role seeding.");
            return;
        }

        _logger.LogInformation("Seeding user roles...");
        var userRoleAssignments = DefaultUsers.GetUserRoleAssignments();
        var userRoles = new List<UserRole>();

        foreach (var assignment in userRoleAssignments)
        {
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == assignment.Key);
            if (user == null)
            {
                _logger.LogWarning($"User with email {assignment.Key} not found, skipping role assignment.");
                continue;
            }

            foreach (var roleName in assignment.Value)
            {
                var role = await _context.Roles.FirstOrDefaultAsync(r => r.Name == roleName);
                if (role == null)
                {
                    _logger.LogWarning($"Role {roleName} not found, skipping assignment for user {assignment.Key}.");
                    continue;
                }

                var userRole = UserRole.Create(user.Id, role.Id);
                userRoles.Add(userRole);
            }
        }

        await _context.UserRoles.AddRangeAsync(userRoles);
        await _context.SaveChangesAsync();
        
        _logger.LogInformation($"Seeded {userRoles.Count} user role assignments.");
    }

    private async Task SeedRolePermissionsAsync()
    {
        if (await _context.RolePermissions.AnyAsync())
        {
            _logger.LogInformation("Role permissions already exist, skipping role permission seeding.");
            return;
        }

        _logger.LogInformation("Seeding role permissions...");
        var rolePermissions = DefaultPermissions.GetRolePermissions();
        var rolePermissionEntities = new List<RolePermission>();

        foreach (var rolePermissionSet in rolePermissions)
        {
            var role = await _context.Roles.FirstOrDefaultAsync(r => r.Name == rolePermissionSet.Key);
            if (role == null)
            {
                _logger.LogWarning($"Role {rolePermissionSet.Key} not found, skipping permission assignment.");
                continue;
            }

            foreach (var permission in rolePermissionSet.Value)
            {
                var dbPermission = await _context.Permissions.FirstOrDefaultAsync(p => p.Name == permission.Name);
                if (dbPermission == null)
                {
                    _logger.LogWarning($"Permission {permission.Name} not found, skipping assignment to role {rolePermissionSet.Key}.");
                    continue;
                }

                var rolePermission = RolePermission.Create(role.Id, dbPermission.Id);
                rolePermissionEntities.Add(rolePermission);
            }
        }

        await _context.RolePermissions.AddRangeAsync(rolePermissionEntities);
        await _context.SaveChangesAsync();
        
        _logger.LogInformation($"Seeded {rolePermissionEntities.Count} role permission assignments.");
    }
}

public static class UserManagementDataSeederExtensions
{
    public static async Task SeedUserManagementDataAsync(this IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<UserManagementDbContext>();
        var passwordHasher = scope.ServiceProvider.GetRequiredService<IPasswordHasher>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<UserManagementDataSeeder>>();
        
        var seeder = new UserManagementDataSeeder(context, passwordHasher, logger);
        await seeder.SeedAsync();
    }
}