using HarmoniHSE360.BuildingBlocks.Application.Commands;
using HarmoniHSE360.Modules.UserManagement.Domain.Users;

namespace HarmoniHSE360.Modules.UserManagement.Application.Commands.RegisterUser;

public sealed record RegisterUserCommand(
    string Email,
    string Password,
    string FirstName,
    string LastName,
    string? PhoneNumber,
    AuthenticationSource AuthenticationSource = AuthenticationSource.Local,
    string? ExternalId = null,
    string? Department = null,
    string? Position = null
) : ICommand<Guid>;