using HarmoniHSE360.Modules.UserManagement.Domain.Users;

namespace HarmoniHSE360.Modules.UserManagement.Infrastructure.Persistence.DataSeeding;

public static class DefaultUsers
{
    public static List<User> GetDefaultUsers()
    {
        return new List<User>
        {
            // System Administrator
            User.Create(
                email: "<EMAIL>",
                firstName: "System",
                lastName: "Administrator",
                phoneNumber: "******-0100",
                department: "IT",
                position: "System Administrator",
                employeeId: "SYS001",
                authenticationSource: AuthenticationSource.Local
            ),

            // HSE Manager
            User.Create(
                email: "<EMAIL>",
                firstName: "<PERSON>",
                lastName: "<PERSON>",
                phoneNumber: "******-0101",
                department: "Health, Safety & Environment",
                position: "HSE Manager",
                employeeId: "HSE001",
                authenticationSource: AuthenticationSource.Local
            ),

            // Department Head - Engineering
            User.Create(
                email: "<EMAIL>",
                firstName: "<PERSON>",
                lastName: "<PERSON>",
                phoneNumber: "******-0102",
                department: "Engineering",
                position: "Department Head",
                employeeId: "ENG001",
                authenticationSource: AuthenticationSource.Local
            ),

            // Department Head - Operations
            User.Create(
                email: "<EMAIL>",
                firstName: "Emily",
                lastName: "Rodriguez",
                phoneNumber: "******-0103",
                department: "Operations",
                position: "Department Head",
                employeeId: "OPS001",
                authenticationSource: AuthenticationSource.Local
            ),

            // Employee - Safety Officer
            User.Create(
                email: "<EMAIL>",
                firstName: "David",
                lastName: "Thompson",
                phoneNumber: "******-0104",
                department: "Health, Safety & Environment",
                position: "Safety Officer",
                employeeId: "HSE002",
                authenticationSource: AuthenticationSource.Local
            ),

            // Employee - Engineer
            User.Create(
                email: "<EMAIL>",
                firstName: "Jennifer",
                lastName: "Martinez",
                phoneNumber: "******-0105",
                department: "Engineering",
                position: "Senior Engineer",
                employeeId: "ENG002",
                authenticationSource: AuthenticationSource.Local
            ),

            // Employee - Technician
            User.Create(
                email: "<EMAIL>",
                firstName: "Robert",
                lastName: "Wilson",
                phoneNumber: "******-0106",
                department: "Operations",
                position: "Senior Technician",
                employeeId: "OPS002",
                authenticationSource: AuthenticationSource.Local
            ),

            // Contractor
            User.Create(
                email: "<EMAIL>",
                firstName: "Amanda",
                lastName: "Davis",
                phoneNumber: "******-0107",
                department: "External Contractor",
                position: "Electrical Contractor",
                employeeId: "CTR001",
                authenticationSource: AuthenticationSource.Local
            ),

            // Student
            User.Create(
                email: "<EMAIL>",
                firstName: "Kevin",
                lastName: "Brown",
                phoneNumber: "******-0108",
                department: "Student Services",
                position: "Graduate Student",
                employeeId: "STU001",
                authenticationSource: AuthenticationSource.Local
            ),

            // Parent
            User.Create(
                email: "<EMAIL>",
                firstName: "Lisa",
                lastName: "Anderson",
                phoneNumber: "******-0109",
                department: "External",
                position: "Parent/Guardian",
                employeeId: "PAR001",
                authenticationSource: AuthenticationSource.Local
            )
        };
    }

    public static Dictionary<string, string[]> GetUserRoleAssignments()
    {
        return new Dictionary<string, string[]>
        {
            ["<EMAIL>"] = new[] { "SystemAdministrator" },
            ["<EMAIL>"] = new[] { "HSEManager" },
            ["<EMAIL>"] = new[] { "DepartmentHead" },
            ["<EMAIL>"] = new[] { "DepartmentHead" },
            ["<EMAIL>"] = new[] { "Employee" },
            ["<EMAIL>"] = new[] { "Employee" },
            ["<EMAIL>"] = new[] { "Employee" },
            ["<EMAIL>"] = new[] { "Contractor" },
            ["<EMAIL>"] = new[] { "Student" },
            ["<EMAIL>"] = new[] { "Parent" }
        };
    }

    public static void SetDefaultPasswords(List<User> users, Func<string, string> hashPassword)
    {
        // Default password: HarmoniHSE360!
        var defaultPassword = "HarmoniHSE360!";
        var defaultPasswordHash = hashPassword(defaultPassword);
        
        foreach (var user in users)
        {
            user.SetPasswordHash(defaultPasswordHash);
        }
    }

    public static Dictionary<string, object> GetUserProfiles()
    {
        return new Dictionary<string, object>
        {
            ["<EMAIL>"] = new
            {
                Bio = "System Administrator with full access to all HarmoniHSE360 modules and configurations.",
                Preferences = new { Theme = "Dark", Language = "en-US", TimeZone = "UTC" },
                LastLoginLocation = "System Console"
            },
            ["<EMAIL>"] = new
            {
                Bio = "Experienced HSE Manager responsible for overall health, safety, and environmental compliance.",
                Certifications = new[] { "NEBOSH General Certificate", "IOSH Managing Safely", "ISO 14001 Lead Auditor" },
                Preferences = new { Theme = "Light", Language = "en-US", TimeZone = "America/New_York" },
                LastLoginLocation = "HSE Office"
            },
            ["<EMAIL>"] = new
            {
                Bio = "Engineering Department Head with 15+ years of experience in industrial safety management.",
                Certifications = new[] { "Professional Engineer (PE)", "Certified Safety Professional (CSP)" },
                Preferences = new { Theme = "Light", Language = "en-US", TimeZone = "America/New_York" },
                LastLoginLocation = "Engineering Wing"
            },
            ["<EMAIL>"] = new
            {
                Bio = "Operations Department Head focused on safe and efficient facility operations.",
                Certifications = new[] { "Operations Management Certificate", "OSHA 30-Hour General Industry" },
                Preferences = new { Theme = "Light", Language = "en-US", TimeZone = "America/New_York" },
                LastLoginLocation = "Operations Center"
            },
            ["<EMAIL>"] = new
            {
                Bio = "Dedicated Safety Officer ensuring compliance with safety protocols and incident management.",
                Certifications = new[] { "OSHA 30-Hour Construction", "First Aid/CPR Certified", "Incident Investigation" },
                Preferences = new { Theme = "Light", Language = "en-US", TimeZone = "America/New_York" },
                LastLoginLocation = "Safety Office"
            },
            ["<EMAIL>"] = new
            {
                Bio = "Senior Engineer specializing in process safety and risk assessment.",
                Certifications = new[] { "Process Safety Management", "Hazard Analysis (HAZOP)" },
                Preferences = new { Theme = "Light", Language = "en-US", TimeZone = "America/New_York" },
                LastLoginLocation = "Engineering Lab"
            },
            ["<EMAIL>"] = new
            {
                Bio = "Experienced technician with expertise in equipment maintenance and safety procedures.",
                Certifications = new[] { "OSHA 10-Hour General Industry", "Confined Space Entry", "Lockout/Tagout" },
                Preferences = new { Theme = "Light", Language = "en-US", TimeZone = "America/New_York" },
                LastLoginLocation = "Maintenance Shop"
            },
            ["<EMAIL>"] = new
            {
                Bio = "Licensed electrical contractor working on facility upgrades and maintenance.",
                Certifications = new[] { "Licensed Electrician", "OSHA 10-Hour Construction", "Arc Flash Safety" },
                Preferences = new { Theme = "Light", Language = "en-US", TimeZone = "America/New_York" },
                LastLoginLocation = "Contractor Area"
            },
            ["<EMAIL>"] = new
            {
                Bio = "Graduate student conducting research in environmental safety and sustainability.",
                Preferences = new { Theme = "Light", Language = "en-US", TimeZone = "America/New_York" },
                LastLoginLocation = "University Lab"
            },
            ["<EMAIL>"] = new
            {
                Bio = "Parent/Guardian with access to safety notifications and incident reports affecting students.",
                Preferences = new { Theme = "Light", Language = "en-US", TimeZone = "America/New_York" },
                NotificationPreferences = new { EmailAlerts = true, SMSAlerts = true, PushNotifications = false }
            }
        };
    }
}