using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using HarmoniHSE360.Modules.UserManagement.Application.Contracts;
using HarmoniHSE360.Modules.UserManagement.Infrastructure.Persistence;
using HarmoniHSE360.Modules.UserManagement.Infrastructure.Persistence.Repositories;
using HarmoniHSE360.Modules.UserManagement.Infrastructure.Services;
using HarmoniHSE360.Modules.UserManagement.Infrastructure.Persistence.DataSeeding;
using HarmoniHSE360.BuildingBlocks.Application;

namespace HarmoniHSE360.Modules.UserManagement.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddUserManagementInfrastructure(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        services.AddDbContext<UserManagementDbContext>(options =>
            options.UseNpgsql(
                configuration.GetConnectionString("DefaultConnection"),
                b => b.MigrationsHistoryTable("__EFMigrationsHistory", "user_management")));

        var jwtSettings = new JwtSettings();
        configuration.GetSection("JwtSettings").Bind(jwtSettings);
        services.Configure<JwtSettings>(options => 
        {
            options.SecretKey = jwtSettings.SecretKey;
            options.Issuer = jwtSettings.Issuer;
            options.Audience = jwtSettings.Audience;
            options.AccessTokenExpiryMinutes = jwtSettings.AccessTokenExpiryMinutes;
            options.RefreshTokenExpiryDays = jwtSettings.RefreshTokenExpiryDays;
        });

        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<IUserRoleRepository, UserRoleRepository>();
        services.AddScoped<IUserLoginRepository, UserLoginRepository>();
        services.AddScoped<IPasswordHasher, PasswordHasher>();
        services.AddScoped<ITokenService, TokenService>();
        services.AddScoped<IUserDomainService, UserDomainService>();
        
        // Register data seeder
        services.AddScoped<UserManagementDataSeeder>();

        return services;
    }

    public static IServiceCollection AddUserManagementModule(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Add MediatR for the UserManagement module
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(
            typeof(HarmoniHSE360.Modules.UserManagement.Application.Commands.AuthenticateUser.AuthenticateUserCommand).Assembly));

        // Add UserManagement Infrastructure
        services.AddUserManagementInfrastructure(configuration);

        return services;
    }
}