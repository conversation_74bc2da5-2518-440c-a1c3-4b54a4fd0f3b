// HarmoniHSE360 Main SCSS Entry File
// This file imports all SCSS partials in the correct order

// 1. Abstracts - Variables, Functions, Mixins
@import 'abstracts/variables';
@import 'abstracts/functions';
@import 'abstracts/mixins';

// 2. Base - Reset, Typography, Utilities
@import 'base/reset';
@import 'base/typography';
@import 'base/utilities';

// 3. Themes - Color schemes and theme-specific styles
@import 'themes/default';
@import 'themes/dark';

// 4. Layouts - Major layout components
@import 'layouts/header';
@import 'layouts/sidebar';
@import 'layouts/footer';
@import 'layouts/grid';

// 5. Components - Reusable component styles
@import 'components/buttons';
@import 'components/forms';
@import 'components/cards';
@import 'components/alerts';
@import 'components/modals';
@import 'components/tables';

// 6. Modules - Page-specific or feature-specific styles
@import 'modules/dashboard';
@import 'modules/hse-modules';
@import 'modules/reporting';
@import 'modules/user-management';

// 7. Vendor overrides - Ant Design customizations
@import 'vendor/ant-design-overrides';