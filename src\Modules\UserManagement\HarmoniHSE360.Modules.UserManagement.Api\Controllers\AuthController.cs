using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HarmoniHSE360.Modules.UserManagement.Application.Commands.RegisterUser;
using HarmoniHSE360.Modules.UserManagement.Application.Commands.AuthenticateUser;
using HarmoniHSE360.Modules.UserManagement.Application.Queries.GetUser;

namespace HarmoniHSE360.Modules.UserManagement.Api.Controllers;

[ApiController]
[Route("api/auth")]
public sealed class AuthController : ControllerBase
{
    private readonly IMediator _mediator;

    public AuthController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPost("register")]
    public async Task<IActionResult> Register([FromBody] RegisterUserRequest request, CancellationToken cancellationToken)
    {
        var command = new RegisterUserCommand(
            request.Email,
            request.Password,
            request.FirstName,
            request.LastName,
            request.PhoneNumber,
            request.AuthenticationSource,
            request.ExternalId,
            request.Department,
            request.Position
        );

        var userId = await _mediator.Send(command, cancellationToken);

        return CreatedAtRoute(
            "GetUser",
            new { id = userId },
            new { UserId = userId });
    }

    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] LoginRequest request, CancellationToken cancellationToken)
    {
        var command = new AuthenticateUserCommand(
            request.Email,
            request.Password,
            GetClientIpAddress(),
            Request.Headers["User-Agent"].ToString()
        );

        var result = await _mediator.Send(command, cancellationToken);

        return Ok(new LoginResponse
        {
            AccessToken = result.AccessToken,
            RefreshToken = result.RefreshToken,
            ExpiresAt = result.ExpiresAt,
            RequiresTwoFactor = result.RequiresTwoFactor
        });
    }

    [HttpGet("me")]
    [Authorize]
    public async Task<IActionResult> GetCurrentUser(CancellationToken cancellationToken)
    {
        var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
        if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
        {
            return Unauthorized();
        }

        var query = new GetUserQuery(userId);
        var user = await _mediator.Send(query, cancellationToken);

        return Ok(user);
    }

    private string? GetClientIpAddress()
    {
        return Request.Headers.ContainsKey("X-Forwarded-For")
            ? Request.Headers["X-Forwarded-For"].FirstOrDefault()?.Split(',').FirstOrDefault()?.Trim()
            : HttpContext.Connection.RemoteIpAddress?.ToString();
    }
}