/* HarmoniHSE360 Base Styling - Following Brand Guidelines */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

/* CSS Variables for HarmoniHSE360 Color Palette */
:root {
  /* Brand Colors */
  --harmoni-teal-primary: #0097A7;
  --harmoni-deep-blue: #004D6E;
  --harmoni-leaf-green: #66BB6A;
  --harmoni-accent-yellow: #F9A825;
  --harmoni-soft-grey: #F5F5F5;
  --harmoni-charcoal: #212121;
  
  /* Additional UI Colors */
  --harmoni-white: #FFFFFF;
  --harmoni-light-grey: #CCCCCC;
  --harmoni-border-grey: #E0E0E0;
  --harmoni-success: #4CAF50;
  --harmoni-warning: #FF9800;
  --harmoni-error: #F44336;
  --harmoni-info: #2196F3;
  
  /* Typography */
  --harmoni-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --harmoni-font-display: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Spacing */
  --harmoni-spacing-xs: 4px;
  --harmoni-spacing-sm: 8px;
  --harmoni-spacing-md: 16px;
  --harmoni-spacing-lg: 24px;
  --harmoni-spacing-xl: 32px;
  --harmoni-spacing-xxl: 48px;
  
  /* Border Radius */
  --harmoni-radius-sm: 4px;
  --harmoni-radius-md: 8px;
  --harmoni-radius-lg: 12px;
  --harmoni-radius-xl: 16px;
  
  /* Shadows */
  --harmoni-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --harmoni-shadow-md: 0 2px 6px rgba(0, 0, 0, 0.1);
  --harmoni-shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  /* Transitions */
  --harmoni-transition: all 0.2s ease-in-out;
  
  /* Breakpoints */
  --harmoni-breakpoint-sm: 600px;
  --harmoni-breakpoint-md: 1024px;
  --harmoni-breakpoint-lg: 1440px;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--harmoni-font-primary);
  font-size: 16px;
  line-height: 1.5;
  color: var(--harmoni-charcoal);
  background-color: var(--harmoni-soft-grey);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Styles */
.harmoni-display-title {
  font-family: var(--harmoni-font-display);
  font-weight: 700;
  font-size: clamp(28px, 4vw, 48px);
  line-height: 1.2;
  color: var(--harmoni-charcoal);
  margin: 0 0 var(--harmoni-spacing-lg) 0;
}

.harmoni-section-header {
  font-family: var(--harmoni-font-display);
  font-weight: 600;
  font-size: clamp(20px, 3vw, 30px);
  line-height: 1.3;
  color: var(--harmoni-charcoal);
  margin: 0 0 var(--harmoni-spacing-md) 0;
}

.harmoni-body-text {
  font-family: var(--harmoni-font-primary);
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5;
  color: var(--harmoni-charcoal);
  margin: 0 0 var(--harmoni-spacing-md) 0;
}

.harmoni-caption {
  font-family: var(--harmoni-font-primary);
  font-weight: 500;
  font-size: 12px;
  line-height: 1.4;
  color: #666666;
  margin: 0;
}

.harmoni-button-text {
  font-family: var(--harmoni-font-primary);
  font-weight: 500;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 0.5px;
}

/* Button Styles */
.harmoni-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--harmoni-spacing-sm);
  padding: 12px 24px;
  border: none;
  border-radius: var(--harmoni-radius-md);
  font-family: var(--harmoni-font-primary);
  font-weight: 500;
  font-size: 14px;
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: var(--harmoni-transition);
  min-height: 44px; /* Mobile touch target */
  min-width: 44px;
  position: relative;
  overflow: hidden;
}

.harmoni-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.harmoni-btn-primary {
  background-color: var(--harmoni-teal-primary);
  color: var(--harmoni-white);
  box-shadow: var(--harmoni-shadow-sm);
}

.harmoni-btn-primary:hover:not(:disabled) {
  background-color: var(--harmoni-deep-blue);
  box-shadow: var(--harmoni-shadow-md);
  transform: translateY(-1px);
}

.harmoni-btn-secondary {
  background-color: transparent;
  color: var(--harmoni-teal-primary);
  border: 2px solid var(--harmoni-teal-primary);
}

.harmoni-btn-secondary:hover:not(:disabled) {
  background-color: var(--harmoni-teal-primary);
  color: var(--harmoni-white);
}

.harmoni-btn-success {
  background-color: var(--harmoni-leaf-green);
  color: var(--harmoni-white);
}

.harmoni-btn-success:hover:not(:disabled) {
  background-color: var(--harmoni-success);
}

.harmoni-btn-warning {
  background-color: var(--harmoni-accent-yellow);
  color: var(--harmoni-charcoal);
}

.harmoni-btn-warning:hover:not(:disabled) {
  background-color: var(--harmoni-warning);
  color: var(--harmoni-white);
}

.harmoni-btn-danger {
  background-color: var(--harmoni-error);
  color: var(--harmoni-white);
}

.harmoni-btn-danger:hover:not(:disabled) {
  background-color: #d32f2f;
}

.harmoni-btn-sm {
  padding: 8px 16px;
  font-size: 12px;
  min-height: 36px;
}

.harmoni-btn-lg {
  padding: 16px 32px;
  font-size: 16px;
  min-height: 52px;
}

/* Card and Container Styles */
.harmoni-card {
  background-color: var(--harmoni-white);
  border-radius: var(--harmoni-radius-md);
  box-shadow: var(--harmoni-shadow-md);
  padding: var(--harmoni-spacing-lg);
  margin-bottom: var(--harmoni-spacing-md);
  border: 1px solid var(--harmoni-border-grey);
}

.harmoni-card-header {
  margin-bottom: var(--harmoni-spacing-md);
  padding-bottom: var(--harmoni-spacing-md);
  border-bottom: 1px solid var(--harmoni-border-grey);
}

.harmoni-card-body {
  margin-bottom: var(--harmoni-spacing-md);
}

.harmoni-card-footer {
  margin-top: var(--harmoni-spacing-md);
  padding-top: var(--harmoni-spacing-md);
  border-top: 1px solid var(--harmoni-border-grey);
  display: flex;
  gap: var(--harmoni-spacing-md);
  justify-content: flex-end;
}

.harmoni-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--harmoni-spacing-md);
}

/* Form Styles */
.harmoni-form-group {
  margin-bottom: var(--harmoni-spacing-md);
}

.harmoni-form-label {
  display: block;
  font-family: var(--harmoni-font-primary);
  font-weight: 500;
  font-size: 14px;
  color: var(--harmoni-charcoal);
  margin-bottom: var(--harmoni-spacing-xs);
}

.harmoni-form-label.required::after {
  content: ' *';
  color: var(--harmoni-error);
}

.harmoni-form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--harmoni-light-grey);
  border-radius: var(--harmoni-radius-md);
  font-family: var(--harmoni-font-primary);
  font-size: 14px;
  line-height: 1.4;
  background-color: var(--harmoni-white);
  transition: var(--harmoni-transition);
  min-height: 44px; /* Mobile touch target */
}

.harmoni-form-input:focus {
  outline: none;
  border-color: var(--harmoni-teal-primary);
  box-shadow: 0 0 0 3px rgba(0, 151, 167, 0.1);
}

.harmoni-form-input::placeholder {
  color: #999999;
  font-weight: 300;
}

.harmoni-form-input.error {
  border-color: var(--harmoni-error);
}

.harmoni-form-error {
  font-size: 12px;
  color: var(--harmoni-error);
  margin-top: var(--harmoni-spacing-xs);
  display: block;
}

.harmoni-form-help {
  font-size: 12px;
  color: #666666;
  margin-top: var(--harmoni-spacing-xs);
  display: block;
}

/* Checkbox and Radio Styles */
.harmoni-checkbox,
.harmoni-radio {
  display: flex;
  align-items: center;
  gap: var(--harmoni-spacing-sm);
  cursor: pointer;
  margin-bottom: var(--harmoni-spacing-sm);
}

.harmoni-checkbox input,
.harmoni-radio input {
  width: 20px;
  height: 20px;
  accent-color: var(--harmoni-teal-primary);
}

/* Alert Styles */
.harmoni-alert {
  padding: var(--harmoni-spacing-md);
  border-radius: var(--harmoni-radius-md);
  margin-bottom: var(--harmoni-spacing-md);
  border-left: 4px solid;
  display: flex;
  align-items: flex-start;
  gap: var(--harmoni-spacing-md);
}

.harmoni-alert-success {
  background-color: #E8F5E8;
  border-left-color: var(--harmoni-success);
  color: #2E7D32;
}

.harmoni-alert-info {
  background-color: #E3F2FD;
  border-left-color: var(--harmoni-info);
  color: #1565C0;
}

.harmoni-alert-warning {
  background-color: #FFF3E0;
  border-left-color: var(--harmoni-warning);
  color: #E65100;
}

.harmoni-alert-error {
  background-color: #FFEBEE;
  border-left-color: var(--harmoni-error);
  color: #C62828;
}

/* Navigation Styles */
.harmoni-navbar {
  background-color: var(--harmoni-white);
  box-shadow: var(--harmoni-shadow-sm);
  border-bottom: 1px solid var(--harmoni-border-grey);
  padding: var(--harmoni-spacing-md) 0;
}

.harmoni-navbar-brand {
  font-family: var(--harmoni-font-display);
  font-weight: 700;
  font-size: 24px;
  color: var(--harmoni-teal-primary);
  text-decoration: none;
}

.harmoni-navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--harmoni-spacing-lg);
  list-style: none;
  margin: 0;
  padding: 0;
}

.harmoni-navbar-link {
  color: var(--harmoni-charcoal);
  text-decoration: none;
  font-weight: 500;
  transition: var(--harmoni-transition);
  padding: var(--harmoni-spacing-sm) var(--harmoni-spacing-md);
  border-radius: var(--harmoni-radius-sm);
}

.harmoni-navbar-link:hover {
  color: var(--harmoni-teal-primary);
  background-color: rgba(0, 151, 167, 0.1);
}

.harmoni-navbar-link.active {
  color: var(--harmoni-teal-primary);
  background-color: rgba(0, 151, 167, 0.1);
}

/* Loading Spinner */
.harmoni-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: harmoniSpin 1s linear infinite;
}

@keyframes harmoniSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Utility Classes */
.harmoni-text-center { text-align: center; }
.harmoni-text-left { text-align: left; }
.harmoni-text-right { text-align: right; }

.harmoni-text-primary { color: var(--harmoni-teal-primary); }
.harmoni-text-success { color: var(--harmoni-success); }
.harmoni-text-warning { color: var(--harmoni-warning); }
.harmoni-text-error { color: var(--harmoni-error); }
.harmoni-text-muted { color: #666666; }

.harmoni-bg-primary { background-color: var(--harmoni-teal-primary); }
.harmoni-bg-light { background-color: var(--harmoni-soft-grey); }
.harmoni-bg-white { background-color: var(--harmoni-white); }

.harmoni-d-flex { display: flex; }
.harmoni-d-block { display: block; }
.harmoni-d-none { display: none; }

.harmoni-align-center { align-items: center; }
.harmoni-justify-center { justify-content: center; }
.harmoni-justify-between { justify-content: space-between; }
.harmoni-justify-end { justify-content: flex-end; }

.harmoni-gap-sm { gap: var(--harmoni-spacing-sm); }
.harmoni-gap-md { gap: var(--harmoni-spacing-md); }
.harmoni-gap-lg { gap: var(--harmoni-spacing-lg); }

.harmoni-mb-0 { margin-bottom: 0; }
.harmoni-mb-sm { margin-bottom: var(--harmoni-spacing-sm); }
.harmoni-mb-md { margin-bottom: var(--harmoni-spacing-md); }
.harmoni-mb-lg { margin-bottom: var(--harmoni-spacing-lg); }

.harmoni-mt-0 { margin-top: 0; }
.harmoni-mt-sm { margin-top: var(--harmoni-spacing-sm); }
.harmoni-mt-md { margin-top: var(--harmoni-spacing-md); }
.harmoni-mt-lg { margin-top: var(--harmoni-spacing-lg); }

.harmoni-p-sm { padding: var(--harmoni-spacing-sm); }
.harmoni-p-md { padding: var(--harmoni-spacing-md); }
.harmoni-p-lg { padding: var(--harmoni-spacing-lg); }

/* Responsive Design */
@media (max-width: 600px) {
  .harmoni-container {
    padding: 0 var(--harmoni-spacing-sm);
  }
  
  .harmoni-card {
    padding: var(--harmoni-spacing-md);
  }
  
  .harmoni-navbar-nav {
    flex-direction: column;
    gap: var(--harmoni-spacing-sm);
  }
  
  .harmoni-btn {
    width: 100%;
    justify-content: center;
  }
  
  .harmoni-card-footer {
    flex-direction: column;
  }
}

@media (max-width: 1024px) {
  .harmoni-display-title {
    font-size: clamp(24px, 3.5vw, 36px);
  }
  
  .harmoni-section-header {
    font-size: clamp(18px, 2.5vw, 24px);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus visible for keyboard navigation */
.harmoni-btn:focus-visible,
.harmoni-form-input:focus-visible,
.harmoni-navbar-link:focus-visible {
  outline: 2px solid var(--harmoni-teal-primary);
  outline-offset: 2px;
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
  :root {
    --harmoni-soft-grey: #1a1a1a;
    --harmoni-white: #2d2d2d;
    --harmoni-charcoal: #e0e0e0;
    --harmoni-border-grey: #404040;
  }
}