using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HarmoniHSE360.Modules.UserManagement.Domain.Users;

namespace HarmoniHSE360.Modules.UserManagement.Infrastructure.Persistence.Configurations;

public sealed class UserLoginConfiguration : IEntityTypeConfiguration<UserLogin>
{
    public void Configure(EntityTypeBuilder<UserLogin> builder)
    {
        builder.ToTable("user_logins");

        builder.HasKey(ul => ul.Id);

        builder.Property(ul => ul.Id)
            .HasColumnName("id")
            .ValueGeneratedNever();

        builder.Property(ul => ul.UserId)
            .HasColumnName("user_id")
            .IsRequired();

        builder.Property(ul => ul.IpAddress)
            .HasColumnName("ip_address")
            .HasMaxLength(45);

        builder.Property(ul => ul.UserAgent)
            .HasColumnName("user_agent")
            .HasMaxLength(500);

        builder.Property(ul => ul.IsSuccessful)
            .HasColumnName("is_successful")
            .IsRequired();

        builder.Property(ul => ul.AttemptedAt)
            .HasColumnName("attempted_at")
            .IsRequired();

        builder.HasOne<User>()
            .WithMany()
            .HasForeignKey(ul => ul.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(ul => ul.UserId)
            .HasDatabaseName("ix_user_logins_user_id");

        builder.HasIndex(ul => ul.AttemptedAt)
            .HasDatabaseName("ix_user_logins_attempted_at");

        builder.HasIndex(ul => new { ul.UserId, ul.IsSuccessful, ul.AttemptedAt })
            .HasDatabaseName("ix_user_logins_user_success_time");
    }
}