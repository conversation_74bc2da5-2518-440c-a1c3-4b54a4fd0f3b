@using Microsoft.AspNetCore.Components.Authorization
@using HarmoniHSE360.BlazorServer.Services
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject AuthService AuthService

<nav class="harmoni-navbar">
    <div class="harmoni-container navbar-container">
        <div class="navbar-brand-section">
            <a href="/" class="harmoni-navbar-brand">
                <div class="brand-logo-nav">
                    <span class="brand-icon">🛡️</span>
                    <span class="brand-text">HarmoniHSE360</span>
                </div>
            </a>
        </div>
        
        <div class="navbar-menu @(isMenuOpen ? "menu-open" : "")">
            <ul class="harmoni-navbar-nav">
                <AuthorizeView>
                    <Authorized>
                        <!-- Dashboard - Available to all authenticated users -->
                        <li class="nav-item">
                            <NavLink class="harmoni-navbar-link" href="/dashboard" Match="NavLinkMatch.All">
                                <span class="nav-icon">📊</span>
                                <span>Dashboard</span>
                            </NavLink>
                        </li>
                        
                        <!-- Incident Management - Available to HSE Staff and Managers -->
                        <AuthorizeView Roles="HSEManager,SystemAdministrator,Employee,DepartmentHead">
                            <Authorized Context="mainAuth">
                                <li class="nav-item dropdown">
                                    <button class="harmoni-navbar-link dropdown-toggle" @onclick="ToggleIncidentDropdown">
                                        <span class="nav-icon">🚨</span>
                                        <span>Incidents</span>
                                        <span class="dropdown-arrow">▼</span>
                                    </button>
                                    <ul class="dropdown-menu @(showIncidentDropdown ? "show" : "")">
                                        <li><NavLink class="dropdown-link" href="/incidents">View All Incidents</NavLink></li>
                                        <li><NavLink class="dropdown-link" href="/incidents/report">Report Incident</NavLink></li>
                                        <AuthorizeView Roles="HSEManager,SystemAdministrator">
                                            <Authorized Context="incidentAuth">
                                                <li><NavLink class="dropdown-link" href="/incidents/analytics">Incident Analytics</NavLink></li>
                                            </Authorized>
                                        </AuthorizeView>
                                    </ul>
                                </li>
                            </Authorized>
                        </AuthorizeView>
                        
                        <!-- Hazard Reporting - Available to all users -->
                        <li class="nav-item dropdown">
                            <button class="harmoni-navbar-link dropdown-toggle" @onclick="ToggleHazardDropdown">
                                <span class="nav-icon">⚠️</span>
                                <span>Hazards</span>
                                <span class="dropdown-arrow">▼</span>
                            </button>
                            <ul class="dropdown-menu @(showHazardDropdown ? "show" : "")">
                                <li><NavLink class="dropdown-link" href="/hazards">View Hazards</NavLink></li>
                                <li><NavLink class="dropdown-link" href="/hazards/report">Report Hazard</NavLink></li>
                                <AuthorizeView Roles="HSEManager,SystemAdministrator,DepartmentHead">
                                    <Authorized Context="hazardAuth">
                                        <li><NavLink class="dropdown-link" href="/hazards/assessments">Risk Assessments</NavLink></li>
                                    </Authorized>
                                </AuthorizeView>
                            </ul>
                        </li>
                        
                        <!-- Training & Compliance -->
                        <li class="nav-item dropdown">
                            <button class="harmoni-navbar-link dropdown-toggle" @onclick="ToggleTrainingDropdown">
                                <span class="nav-icon">🎓</span>
                                <span>Training</span>
                                <span class="dropdown-arrow">▼</span>
                            </button>
                            <ul class="dropdown-menu @(showTrainingDropdown ? "show" : "")">
                                <li><NavLink class="dropdown-link" href="/training/my-courses">My Training</NavLink></li>
                                <li><NavLink class="dropdown-link" href="/training/certificates">Certificates</NavLink></li>
                                <AuthorizeView Roles="HSEManager,SystemAdministrator,DepartmentHead">
                                    <Authorized Context="trainingAuth">
                                        <li><NavLink class="dropdown-link" href="/training/manage">Manage Training</NavLink></li>
                                        <li><NavLink class="dropdown-link" href="/compliance/audits">Compliance Audits</NavLink></li>
                                    </Authorized>
                                </AuthorizeView>
                            </ul>
                        </li>
                        
                        <!-- Documents -->
                        <li class="nav-item">
                            <NavLink class="harmoni-navbar-link" href="/documents">
                                <span class="nav-icon">📄</span>
                                <span>Documents</span>
                            </NavLink>
                        </li>
                        
                        <!-- Administration - Restricted to Managers and Admins -->
                        <AuthorizeView Roles="HSEManager,SystemAdministrator">
                            <Authorized Context="adminMainAuth">
                                <li class="nav-item dropdown">
                                    <button class="harmoni-navbar-link dropdown-toggle" @onclick="ToggleAdminDropdown">
                                        <span class="nav-icon">⚙️</span>
                                        <span>Administration</span>
                                        <span class="dropdown-arrow">▼</span>
                                    </button>
                                    <ul class="dropdown-menu @(showAdminDropdown ? "show" : "")">
                                        <AuthorizeView Roles="SystemAdministrator">
                                            <Authorized Context="adminAuth">
                                                <li><NavLink class="dropdown-link" href="/admin/users">User Management</NavLink></li>
                                                <li><NavLink class="dropdown-link" href="/admin/roles">Roles & Permissions</NavLink></li>
                                                <li><NavLink class="dropdown-link" href="/admin/settings">System Settings</NavLink></li>
                                            </Authorized>
                                        </AuthorizeView>
                                        <li><NavLink class="dropdown-link" href="/admin/analytics">Analytics & Reports</NavLink></li>
                                        <li><NavLink class="dropdown-link" href="/admin/permits">Permit Management</NavLink></li>
                                    </ul>
                                </li>
                            </Authorized>
                        </AuthorizeView>
                    </Authorized>
                    <NotAuthorized>
                        <!-- Public navigation items -->
                        <li class="nav-item">
                            <NavLink class="harmoni-navbar-link" href="/about">
                                <span class="nav-icon">ℹ️</span>
                                <span>About</span>
                            </NavLink>
                        </li>
                    </NotAuthorized>
                </AuthorizeView>
            </ul>
        </div>
        
        <div class="navbar-actions">
            <AuthorizeView>
                <Authorized>
                    <!-- User Profile Dropdown -->
                    <div class="user-menu dropdown">
                        <button class="user-menu-toggle" @onclick="ToggleUserDropdown">
                            <div class="user-avatar">
                                <span class="user-initials">@GetUserInitials(context.User.Identity?.Name)</span>
                            </div>
                            <div class="user-info">
                                <span class="user-name">@GetDisplayName(context.User.Identity?.Name)</span>
                                <span class="user-role">@GetUserRole(context.User)</span>
                            </div>
                            <span class="dropdown-arrow">▼</span>
                        </button>
                        <ul class="dropdown-menu user-dropdown @(showUserDropdown ? "show" : "")">
                            <li class="dropdown-header">
                                <div class="user-details">
                                    <strong>@GetDisplayName(context.User.Identity?.Name)</strong>
                                    <small>@context.User.Identity?.Name</small>
                                </div>
                            </li>
                            <li class="dropdown-divider"></li>
                            <li><NavLink class="dropdown-link" href="/profile">👤 My Profile</NavLink></li>
                            <li><NavLink class="dropdown-link" href="/settings">⚙️ Settings</NavLink></li>
                            <li><NavLink class="dropdown-link" href="/help">❓ Help & Support</NavLink></li>
                            <li class="dropdown-divider"></li>
                            <li>
                                <button class="dropdown-link logout-btn" @onclick="HandleLogout">
                                    🚪 Sign Out
                                </button>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- Notifications -->
                    <button class="notification-btn" title="Notifications">
                        <span class="notification-icon">🔔</span>
                        <span class="notification-badge">3</span>
                    </button>
                    
                    <!-- Language Toggle -->
                    <button class="language-toggle" @onclick="ToggleLanguage" title="Switch Language">
                        <span class="language-icon">🌐</span>
                        <span class="language-text">@currentLanguage</span>
                    </button>
                </Authorized>
                <NotAuthorized>
                    <NavLink class="harmoni-btn harmoni-btn-secondary" href="/login">
                        Sign In
                    </NavLink>
                </NotAuthorized>
            </AuthorizeView>
            
            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" @onclick="ToggleMobileMenu" aria-label="Toggle navigation menu">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>
    </div>
</nav>


@code {
    private bool isMenuOpen = false;
    private bool showIncidentDropdown = false;
    private bool showHazardDropdown = false;
    private bool showTrainingDropdown = false;
    private bool showAdminDropdown = false;
    private bool showUserDropdown = false;
    private string currentLanguage = "EN";

    private void ToggleMobileMenu()
    {
        isMenuOpen = !isMenuOpen;
    }

    private void ToggleIncidentDropdown()
    {
        showIncidentDropdown = !showIncidentDropdown;
        CloseOtherDropdowns("incident");
    }

    private void ToggleHazardDropdown()
    {
        showHazardDropdown = !showHazardDropdown;
        CloseOtherDropdowns("hazard");
    }

    private void ToggleTrainingDropdown()
    {
        showTrainingDropdown = !showTrainingDropdown;
        CloseOtherDropdowns("training");
    }

    private void ToggleAdminDropdown()
    {
        showAdminDropdown = !showAdminDropdown;
        CloseOtherDropdowns("admin");
    }

    private void ToggleUserDropdown()
    {
        showUserDropdown = !showUserDropdown;
        CloseOtherDropdowns("user");
    }

    private void CloseOtherDropdowns(string except)
    {
        if (except != "incident") showIncidentDropdown = false;
        if (except != "hazard") showHazardDropdown = false;
        if (except != "training") showTrainingDropdown = false;
        if (except != "admin") showAdminDropdown = false;
        if (except != "user") showUserDropdown = false;
    }

    private void ToggleLanguage()
    {
        currentLanguage = currentLanguage == "EN" ? "ID" : "EN";
        // TODO: Implement actual language switching logic
    }

    private async Task HandleLogout()
    {
        await AuthService.LogoutAsync();
        Navigation.NavigateTo("/login");
    }

    private string GetUserInitials(string? userName)
    {
        if (string.IsNullOrEmpty(userName))
            return "U";

        var parts = userName.Split('@')[0].Split('.');
        if (parts.Length >= 2)
        {
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        }
        
        return userName.Substring(0, Math.Min(2, userName.Length)).ToUpper();
    }

    private string GetDisplayName(string? userName)
    {
        if (string.IsNullOrEmpty(userName))
            return "User";

        var emailPart = userName.Split('@')[0];
        var parts = emailPart.Split('.');
        
        if (parts.Length >= 2)
        {
            return $"{char.ToUpper(parts[0][0])}{parts[0].Substring(1)} {char.ToUpper(parts[1][0])}{parts[1].Substring(1)}";
        }
        
        return char.ToUpper(emailPart[0]) + emailPart.Substring(1);
    }

    private string GetUserRole(System.Security.Claims.ClaimsPrincipal user)
    {
        var roles = user.Claims
            .Where(c => c.Type == System.Security.Claims.ClaimTypes.Role)
            .Select(c => c.Value)
            .ToList();

        if (roles.Contains("SystemAdministrator"))
            return "System Administrator";
        else if (roles.Contains("HSEManager"))
            return "HSE Manager";
        else if (roles.Contains("DepartmentHead"))
            return "Department Head";
        else if (roles.Contains("Employee"))
            return "Employee";
        else if (roles.Contains("Contractor"))
            return "Contractor";
        else if (roles.Contains("Student"))
            return "Student";
        else if (roles.Contains("Parent"))
            return "Parent";
        
        return "User";
    }

    protected override async Task OnInitializedAsync()
    {
        // Close dropdowns when clicking outside
        await Task.CompletedTask;
    }
}