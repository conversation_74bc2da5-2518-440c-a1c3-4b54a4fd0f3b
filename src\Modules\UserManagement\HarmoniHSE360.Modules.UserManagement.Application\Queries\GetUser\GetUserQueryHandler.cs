using HarmoniHSE360.BuildingBlocks.Application.Queries;
using HarmoniHSE360.Modules.UserManagement.Application.Contracts;

namespace HarmoniHSE360.Modules.UserManagement.Application.Queries.GetUser;

public sealed class GetUserQueryHandler : IQueryHandler<GetUserQuery, UserDto>
{
    private readonly IUserRepository _userRepository;
    private readonly IUserRoleRepository _userRoleRepository;

    public GetUserQueryHandler(IUserRepository userRepository, IUserRoleRepository userRoleRepository)
    {
        _userRepository = userRepository;
        _userRoleRepository = userRoleRepository;
    }

    public async Task<UserDto> Handle(GetUserQuery request, CancellationToken cancellationToken)
    {
        var user = await _userRepository.GetByIdAsync(request.UserId, cancellationToken);
        if (user is null)
        {
            throw new InvalidOperationException($"User with ID {request.UserId} not found");
        }

        var userRoles = await _userRoleRepository.GetUserRolesAsync(request.UserId, cancellationToken);

        return new UserDto(
            user.Id,
            user.Email,
            user.FirstName,
            user.LastName,
            user.PhoneNumber,
            user.Department,
            user.Position,
            user.IsActive,
            user.EmailConfirmed,
            user.TwoFactorEnabled,
            user.CreatedAt,
            user.LastLoginAt,
            userRoles.Select(ur => new UserRoleDto(
                ur.Role.Id,
                ur.Role.Name,
                ur.Role.Description)).ToList()
        );
    }
}