// HarmoniHSE360 SCSS Mixins
// Reusable mixins for common patterns

// ============================================
// Responsive Breakpoints
// ============================================
@mixin breakpoint($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$harmoni-breakpoint-xs}) {
      @content;
    }
  } @else if $breakpoint == sm {
    @media (min-width: #{$harmoni-breakpoint-sm}) {
      @content;
    }
  } @else if $breakpoint == md {
    @media (min-width: #{$harmoni-breakpoint-md}) {
      @content;
    }
  } @else if $breakpoint == lg {
    @media (min-width: #{$harmoni-breakpoint-lg}) {
      @content;
    }
  } @else if $breakpoint == xl {
    @media (min-width: #{$harmoni-breakpoint-xl}) {
      @content;
    }
  } @else if $breakpoint == 2xl {
    @media (min-width: #{$harmoni-breakpoint-2xl}) {
      @content;
    }
  }
}

// ============================================
// Flexbox Utilities
// ============================================
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// ============================================
// Card Styles
// ============================================
@mixin card($padding: $harmoni-spacing-lg, $radius: $harmoni-radius-lg) {
  background-color: white;
  border-radius: $radius;
  box-shadow: $harmoni-shadow-md;
  padding: $padding;
  transition: box-shadow $harmoni-transition;
  
  &:hover {
    box-shadow: $harmoni-shadow-lg;
  }
}

// ============================================
// Button Styles
// ============================================
@mixin button-variant($bg-color, $text-color: white) {
  background-color: $bg-color;
  color: $text-color;
  border: 1px solid $bg-color;
  transition: all $harmoni-transition;
  
  &:hover:not(:disabled) {
    background-color: darken($bg-color, 10%);
    border-color: darken($bg-color, 10%);
  }
  
  &:active:not(:disabled) {
    background-color: darken($bg-color, 15%);
    border-color: darken($bg-color, 15%);
  }
  
  &:focus {
    box-shadow: 0 0 0 2px rgba($bg-color, 0.3);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// ============================================
// Text Truncation
// ============================================
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin line-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// ============================================
// Scrollbar Styling
// ============================================
@mixin custom-scrollbar($thumb-color: $harmoni-cool-grey, $track-color: $harmoni-border-grey) {
  scrollbar-width: thin;
  scrollbar-color: $thumb-color $track-color;
  
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: $harmoni-radius-sm;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $harmoni-radius-sm;
    
    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}

// ============================================
// Gradient Backgrounds
// ============================================
@mixin gradient-primary {
  background: linear-gradient(135deg, $harmoni-teal-primary 0%, $harmoni-deep-blue 100%);
}

@mixin gradient-light {
  background: linear-gradient(135deg, #f8fffe 0%, #e0f7fa 100%);
}

// ============================================
// Focus Styles
// ============================================
@mixin focus-ring($color: $harmoni-teal-primary) {
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba($color, 0.3);
  }
  
  &:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px rgba($color, 0.3);
  }
}

// ============================================
// Animation
// ============================================
@mixin slide-in($direction: left, $distance: 100%) {
  @if $direction == left {
    transform: translateX(-$distance);
  } @else if $direction == right {
    transform: translateX($distance);
  } @else if $direction == top {
    transform: translateY(-$distance);
  } @else if $direction == bottom {
    transform: translateY($distance);
  }
  
  opacity: 0;
  transition: transform $harmoni-transition, opacity $harmoni-transition;
  
  &.show {
    transform: translate(0);
    opacity: 1;
  }
}

// ============================================
// Status Indicators
// ============================================
@mixin status-indicator($color) {
  &::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: $harmoni-radius-full;
    background-color: $color;
    margin-right: $harmoni-spacing-sm;
  }
}