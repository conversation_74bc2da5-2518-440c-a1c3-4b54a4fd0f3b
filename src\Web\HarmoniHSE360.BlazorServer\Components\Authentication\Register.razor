@page "/register"
@using Microsoft.AspNetCore.Components.Forms
@using HarmoniHSE360.BlazorServer.Services
@attribute [Microsoft.AspNetCore.Authorization.AllowAnonymous]
@rendermode InteractiveServer
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@inject AuthService AuthService

<PageTitle>Create Account - HarmoniHSE360</PageTitle>

<div class="register-container">
    <div class="register-wrapper">
        <Card class="register-card">
            <div class="brand-header">
                <div class="brand-logo">
                    <Title Level="1" class="brand-title">HarmoniHSE360</Title>
                    <Text Type="@TextElementType.Secondary" class="brand-subtitle">Create Your Safety Account</Text>
                </div>
            </div>
            
            <div class="register-content">
                <div class="form-section">
                    <Form Model="@registerModel" 
                          OnFinish="@HandleRegister" 
                          OnFinishFailed="@HandleRegisterFailed"
                          Layout="@FormLayout.Vertical"
                          class="register-form"
                          ValidateOnChange="true">
                        
                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <Alert Type="@AlertType.Error" 
                                   Message="@errorMessage" 
                                   ShowIcon="true" 
                                   Closable="true"
                                   OnClose="@(() => errorMessage = string.Empty)" 
                                   Style="margin-bottom: 16px;" />
                        }
                        
                        @if (!string.IsNullOrEmpty(successMessage))
                        {
                            <Alert Type="@AlertType.Success" 
                                   Message="@successMessage" 
                                   ShowIcon="true" 
                                   Closable="true"
                                   OnClose="@(() => successMessage = string.Empty)" 
                                   Style="margin-bottom: 16px;" />
                        }
                        
                        <Row Gutter="16">
                            <Col Span="12">
                                <FormItem Label="First Name" Required="true">
                                    <Input @bind-Value="@registerModel.FirstName" 
                                           Placeholder="Enter your first name"
                                           Size="@InputSize.Large"
                                           AllowClear="true">
                                        <Prefix>
                                            <Icon Type="user" />
                                        </Prefix>
                                    </Input>
                                </FormItem>
                            </Col>
                            <Col Span="12">
                                <FormItem Label="Last Name" Required="true">
                                    <Input @bind-Value="@registerModel.LastName" 
                                           Placeholder="Enter your last name"
                                           Size="@InputSize.Large"
                                           AllowClear="true">
                                        <Prefix>
                                            <Icon Type="user" />
                                        </Prefix>
                                    </Input>
                                </FormItem>
                            </Col>
                        </Row>
                        
                        <FormItem Label="Email Address" Required="true">
                            <Input @bind-Value="@registerModel.Email" 
                                   Type="email"
                                   Placeholder="Enter your work email address"
                                   Size="@InputSize.Large"
                                   AllowClear="true"
>
                                <Prefix>
                                    <Icon Type="mail" />
                                </Prefix>
                            </Input>
                        </FormItem>
                        
                        <FormItem Label="Phone Number">
                            <Input @bind-Value="@registerModel.PhoneNumber" 
                                   Type="tel"
                                   Placeholder="Enter your phone number"
                                   Size="@InputSize.Large"
                                   AllowClear="true"
>
                                <Prefix>
                                    <Icon Type="phone" />
                                </Prefix>
                            </Input>
                        </FormItem>
                        
                        <Row Gutter="16">
                            <Col Span="12">
                                <FormItem Label="Department" Required="true">
                                    <Select TItemValue="string" TItem="string"
                                            @bind-Value="@registerModel.Department"
                                            Placeholder="Select your department"
                                            Size="@InputSize.Large"
                                            AllowClear="true"
                                            ShowSearch="true"
                                            Style="width: 100%;">
                                        <SelectOptions>
                                            <SelectOption TItemValue="string" TItem="string" Value="@("Engineering")" Label="@("Engineering")" />
                                            <SelectOption TItemValue="string" TItem="string" Value="@("Operations")" Label="@("Operations")" />
                                            <SelectOption TItemValue="string" TItem="string" Value="@("HSE")" Label="@("Health Safety and Environment")" />
                                            <SelectOption TItemValue="string" TItem="string" Value="@("HR")" Label="@("Human Resources")" />
                                            <SelectOption TItemValue="string" TItem="string" Value="@("Finance")" Label="@("Finance")" />
                                            <SelectOption TItemValue="string" TItem="string" Value="@("IT")" Label="@("Information Technology")" />
                                            <SelectOption TItemValue="string" TItem="string" Value="@("QA")" Label="@("Quality Assurance")" />
                                            <SelectOption TItemValue="string" TItem="string" Value="@("Maintenance")" Label="@("Maintenance")" />
                                            <SelectOption TItemValue="string" TItem="string" Value="@("Security")" Label="@("Security")" />
                                            <SelectOption TItemValue="string" TItem="string" Value="@("Administration")" Label="@("Administration")" />
                                        </SelectOptions>
                                    </Select>
                                </FormItem>
                            </Col>
                            <Col Span="12">
                                <FormItem Label="Position" Required="true">
                                    <Input @bind-Value="@registerModel.Position" 
                                           Placeholder="Enter your job title"
                                           Size="@InputSize.Large"
                                           AllowClear="true">
                                        <Prefix>
                                            <Icon Type="idcard" />
                                        </Prefix>
                                    </Input>
                                </FormItem>
                            </Col>
                        </Row>
                        
                        <FormItem Label="Employee ID">
                            <Input @bind-Value="@registerModel.EmployeeId" 
                                   Placeholder="Enter your employee ID (if applicable)"
                                   Size="@InputSize.Large"
                                   AllowClear="true">
                                <Prefix>
                                    <Icon Type="number" />
                                </Prefix>
                            </Input>
                        </FormItem>
                        
                        <FormItem Label="Password" Required="true">
                            <InputPassword @bind-Value="@registerModel.Password" 
                                           Placeholder="Create a strong password"
                                           Size="@InputSize.Large"
                                           VisibilityToggle="true">
                                <Prefix>
                                    <Icon Type="lock" />
                                </Prefix>
                            </InputPassword>
                            <div class="password-requirements">
                                <Text Type="@TextElementType.Secondary" class="requirement-text">
                                    Password must contain at least 8 characters with uppercase, lowercase, number, and special character.
                                </Text>
                            </div>
                        </FormItem>
                        
                        <FormItem Label="Confirm Password" Required="true">
                            <InputPassword @bind-Value="@registerModel.ConfirmPassword" 
                                           Placeholder="Confirm your password"
                                           Size="@InputSize.Large"
                                           VisibilityToggle="true">
                                <Prefix>
                                    <Icon Type="lock" />
                                </Prefix>
                            </InputPassword>
                        </FormItem>
                        
                        <FormItem Required="true">
                            <Checkbox @bind-Value="@registerModel.AcceptTerms">
                                I agree to the <a href="/terms" target="_blank">Terms of Service</a> and <a href="/privacy" target="_blank">Privacy Policy</a>
                            </Checkbox>
                        </FormItem>
                        
                        <FormItem>
                            <Checkbox @bind-Value="@registerModel.SubscribeToUpdates">
                                Subscribe to safety updates and notifications
                            </Checkbox>
                        </FormItem>
                        
                        <FormItem>
                            <Button Type="@ButtonType.Primary" 
                                    HtmlType="submit"
                                    Size="@ButtonSize.Large"
                                    Block="true"
                                    Loading="@isLoading">
                                Create Account
                            </Button>
                        </FormItem>
                        
                        <div class="login-link">
                            <Text>
                                Already have an account? 
                                <a href="/login" class="login-link-text">Sign in here</a>
                            </Text>
                        </div>
                    </Form>
                </div>
                
                <div class="info-section">
                    <Card class="info-card" Size="@("small")">
                        <Title Level="4" class="info-title">
                            <Icon Type="safety-certificate" class="info-icon" />
                            Safety First
                        </Title>
                        <div class="info-content">
                            <Paragraph>
                                Join HarmoniHSE360 to access comprehensive safety management tools designed for modern workplaces.
                            </Paragraph>
                            
                            <div class="features-list">
                                <Space Direction="DirectionVHType.Vertical" Size="@("small")" Style="width: 100%;">
                                    <SpaceItem>
                                        <div class="feature-item">
                                            <Icon Type="check-circle" class="feature-icon" />
                                            <Text>Incident Reporting & Management</Text>
                                        </div>
                                    </SpaceItem>
                                    <SpaceItem>
                                        <div class="feature-item">
                                            <Icon Type="check-circle" class="feature-icon" />
                                            <Text>Hazard Identification & Risk Assessment</Text>
                                        </div>
                                    </SpaceItem>
                                    <SpaceItem>
                                        <div class="feature-item">
                                            <Icon Type="check-circle" class="feature-icon" />
                                            <Text>Compliance Auditing & Training</Text>
                                        </div>
                                    </SpaceItem>
                                    <SpaceItem>
                                        <div class="feature-item">
                                            <Icon Type="check-circle" class="feature-icon" />
                                            <Text>Real-time Analytics & Reporting</Text>
                                        </div>
                                    </SpaceItem>
                                </Space>
                            </div>
                            
                            <Divider />
                            
                            <div class="contact-info">
                                <Title Level="5">Need Help?</Title>
                                <Paragraph class="contact-text">
                                    Contact our support team at 
                                    <a href="mailto:<EMAIL>"><EMAIL></a>
                                    or call <a href="tel:******-0199">+1 (555) 0199</a>
                                </Paragraph>
                            </div>
                        </div>
                    </Card>
                </div>
            </div>
        </Card>
    </div>
</div>

@code {
    private RegisterModel registerModel = new();
    private bool isLoading = false;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;

    private async Task HandleRegister(EditContext editContext)
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;
            StateHasChanged();
            
            // Basic validation
            if (registerModel.Password != registerModel.ConfirmPassword)
            {
                errorMessage = "Passwords do not match.";
                return;
            }
            
            if (!registerModel.AcceptTerms)
            {
                errorMessage = "You must accept the Terms of Service and Privacy Policy.";
                return;
            }
            
            // Create registration request
            var registerRequest = new RegisterRequest
            {
                Email = registerModel.Email,
                Password = registerModel.Password,
                FirstName = registerModel.FirstName,
                LastName = registerModel.LastName,
                PhoneNumber = registerModel.PhoneNumber,
                Department = registerModel.Department,
                Position = registerModel.Position,
                AuthenticationSource = "Local"
            };
            
            // Call the authentication service
            var result = await AuthService.RegisterAsync(registerRequest);
            
            if (result.Success)
            {
                successMessage = "Account created successfully! You can now sign in with your credentials.";
                StateHasChanged();
                await Task.Delay(2000);
                Navigation.NavigateTo("/login");
            }
            else
            {
                errorMessage = result.ErrorMessage ?? "Registration failed. Please try again.";
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred during registration. Please try again.";
            StateHasChanged();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task HandleRegisterFailed(EditContext editContext)
    {
        errorMessage = "Please check your input and try again.";
        await InvokeAsync(StateHasChanged);
    }

    public class RegisterModel
    {
        [Required(ErrorMessage = "First name is required")]
        [StringLength(50, ErrorMessage = "First name cannot be longer than 50 characters")]
        public string FirstName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Last name is required")]
        [StringLength(50, ErrorMessage = "Last name cannot be longer than 50 characters")]
        public string LastName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string Email { get; set; } = string.Empty;

        [Phone(ErrorMessage = "Please enter a valid phone number")]
        public string? PhoneNumber { get; set; }

        [Required(ErrorMessage = "Department is required")]
        public string Department { get; set; } = string.Empty;

        [Required(ErrorMessage = "Position is required")]
        [StringLength(100, ErrorMessage = "Position cannot be longer than 100 characters")]
        public string Position { get; set; } = string.Empty;

        public string? EmployeeId { get; set; }

        [Required(ErrorMessage = "Password is required")]
        [StringLength(100, MinimumLength = 8, ErrorMessage = "Password must be at least 8 characters long")]
        [RegularExpression(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]", 
            ErrorMessage = "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character")]
        public string Password { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password confirmation is required")]
        public string ConfirmPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "You must accept the terms and conditions")]
        public bool AcceptTerms { get; set; } = false;

        public bool SubscribeToUpdates { get; set; } = true;
    }
}