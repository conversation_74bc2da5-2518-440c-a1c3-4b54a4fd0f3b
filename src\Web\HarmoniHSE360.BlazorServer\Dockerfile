FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS base
WORKDIR /app
EXPOSE 8080

# Install ICU libraries for globalization support
RUN apk add --no-cache icu-libs icu-data-full

# Set environment variable to enable globalization
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false

# Create non-root user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy solution and project files
COPY ["HarmoniHSE360.sln", "./"]
COPY ["src/BuildingBlocks/HarmoniHSE360.BuildingBlocks.Domain/HarmoniHSE360.BuildingBlocks.Domain.csproj", "src/BuildingBlocks/HarmoniHSE360.BuildingBlocks.Domain/"]
COPY ["src/BuildingBlocks/HarmoniHSE360.BuildingBlocks.Application/HarmoniHSE360.BuildingBlocks.Application.csproj", "src/BuildingBlocks/HarmoniHSE360.BuildingBlocks.Application/"]
COPY ["src/BuildingBlocks/HarmoniHSE360.BuildingBlocks.Infrastructure/HarmoniHSE360.BuildingBlocks.Infrastructure.csproj", "src/BuildingBlocks/HarmoniHSE360.BuildingBlocks.Infrastructure/"]
COPY ["src/BuildingBlocks/HarmoniHSE360.BuildingBlocks.EventBus/HarmoniHSE360.BuildingBlocks.EventBus.csproj", "src/BuildingBlocks/HarmoniHSE360.BuildingBlocks.EventBus/"]
COPY ["src/Web/HarmoniHSE360.BlazorServer/HarmoniHSE360.BlazorServer.csproj", "src/Web/HarmoniHSE360.BlazorServer/"]

# Restore dependencies
RUN dotnet restore "src/Web/HarmoniHSE360.BlazorServer/HarmoniHSE360.BlazorServer.csproj"

# Copy everything else
COPY . .

# Build
WORKDIR "/src/src/Web/HarmoniHSE360.BlazorServer"
RUN dotnet build "HarmoniHSE360.BlazorServer.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "HarmoniHSE360.BlazorServer.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Change to non-root user
USER appuser

ENTRYPOINT ["dotnet", "HarmoniHSE360.BlazorServer.dll"]