# 🔑 HarmoniHSE360 - Seeded User Credentials

> **Quick Reference**: Default login credentials for testing and development

## Default Password for All Users
```
HarmoniHSE360!
```

## 👤 Available Users

| Role | Email | Department | Position |
|------|-------|------------|----------|
| **System Administrator** | `<EMAIL>` | IT | System Administrator |
| **HSE Manager** | `<EMAIL>` | Health, Safety & Environment | HSE Manager |
| **Department Head** | `<EMAIL>` | Engineering | Department Head |
| **Department Head** | `<EMAIL>` | Operations | Department Head |
| **Employee** | `<EMAIL>` | Health, Safety & Environment | Safety Officer |
| **Employee** | `<EMAIL>` | Engineering | Senior Engineer |
| **Employee** | `<EMAIL>` | Operations | Senior Technician |
| **Contractor** | `<EMAIL>` | External Contractor | Electrical Contractor |
| **Student** | `<EMAIL>` | Student Services | Graduate Student |
| **Parent** | `<EMAIL>` | External | Parent/Guardian |

## 🚀 Quick Login Examples

### Admin Access
```
Email: <EMAIL>
Password: HarmoniHSE360!
```

### HSE Manager Access  
```
Email: <EMAIL>
Password: HarmoniHSE360!
```

### Regular Employee Access
```
Email: <EMAIL>
Password: HarmoniHSE360!
```

## 🔗 Useful Links

- **API Swagger**: http://localhost:5000/swagger
- **Blazor UI**: http://localhost:5001
- **Full Documentation**: [Authentication_Guide.md](./Authentication_Guide.md)

## ⚠️ Important Notes

- **Development Only**: These are default credentials for development/testing
- **Change in Production**: All passwords must be changed before production deployment
- **Auto-Seeded**: Users are automatically created when running `docker-compose up -d`
- **Active Status**: All seeded users are active by default

---
*For detailed authentication information, API endpoints, and security features, see the full [Authentication Guide](./Authentication_Guide.md)*