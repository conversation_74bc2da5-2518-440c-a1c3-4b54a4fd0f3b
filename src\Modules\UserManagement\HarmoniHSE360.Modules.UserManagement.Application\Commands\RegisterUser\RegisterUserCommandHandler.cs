using HarmoniHSE360.BuildingBlocks.Application.Commands;
using HarmoniHSE360.Modules.UserManagement.Application.Contracts;
using HarmoniHSE360.Modules.UserManagement.Domain.Users;

namespace HarmoniHSE360.Modules.UserManagement.Application.Commands.RegisterUser;

public sealed class RegisterUserCommandHandler : ICommandHandler<RegisterUserCommand, Guid>
{
    private readonly IUserRepository _userRepository;
    private readonly IPasswordHasher _passwordHasher;
    private readonly IUserDomainService _userDomainService;

    public RegisterUserCommandHandler(
        IUserRepository userRepository,
        IPasswordHasher passwordHasher,
        IUserDomainService userDomainService)
    {
        _userRepository = userRepository;
        _passwordHasher = passwordHasher;
        _userDomainService = userDomainService;
    }

    public async Task<Guid> Handle(RegisterUserCommand request, CancellationToken cancellationToken)
    {
        var existingUser = await _userRepository.GetByEmailAsync(request.Email, cancellationToken);
        if (existingUser is not null)
        {
            throw new InvalidOperationException($"User with email {request.Email} already exists");
        }

        var hashedPassword = _passwordHasher.HashPassword(request.Password);

        var user = await _userDomainService.CreateUserAsync(
            request.Email,
            hashedPassword,
            request.FirstName,
            request.LastName,
            request.PhoneNumber,
            request.AuthenticationSource,
            request.ExternalId,
            request.Department,
            request.Position);

        await _userRepository.AddAsync(user, cancellationToken);

        return user.Id;
    }
}